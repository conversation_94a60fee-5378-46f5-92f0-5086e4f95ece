/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { Screen } from '../App.js';
interface ConfigScreenProps {
    config: Configuration;
    logger: Logger;
    onNavigate: (screen: Screen) => void;
}
export declare function ConfigScreen({ config, logger, onNavigate }: ConfigScreenProps): React.JSX.Element;
export {};
//# sourceMappingURL=ConfigScreen.d.ts.map