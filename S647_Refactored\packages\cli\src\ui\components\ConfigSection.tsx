/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { Box, Text } from 'ink';

/**
 * Config section props
 */
export interface ConfigSectionProps {
  title: string;
  children: React.ReactNode;
  collapsible?: boolean;
  collapsed?: boolean;
  onToggle?: () => void;
}

/**
 * Config section component
 */
export function ConfigSection({
  title,
  children,
  collapsible = false,
  collapsed = false,
  onToggle
}: ConfigSectionProps): React.JSX.Element {
  return (
    <Box flexDirection="column" marginBottom={1}>
      <Box marginBottom={1}>
        <Text color="cyan" bold>
          {collapsible ? (collapsed ? '▶ ' : '▼ ') : '● '}
          {title}
        </Text>
        {collapsible && onToggle && (
          <Box marginLeft={2}>
            <Text color="gray" dimColor>
              (Press Enter to {collapsed ? 'expand' : 'collapse'})
            </Text>
          </Box>
        )}
      </Box>
      
      {!collapsed && (
        <Box flexDirection="column" marginLeft={2}>
          {children}
        </Box>
      )}
    </Box>
  );
}

/**
 * Config item props
 */
export interface ConfigItemProps {
  label: string;
  value: string | number | boolean;
  description?: string;
  editable?: boolean;
  onEdit?: () => void;
}

/**
 * Config item component
 */
export function ConfigItem({
  label,
  value,
  description,
  editable = false,
  onEdit
}: ConfigItemProps): React.JSX.Element {
  const formatValue = (val: string | number | boolean): string => {
    if (typeof val === 'boolean') {
      return val ? '✅ Enabled' : '❌ Disabled';
    }
    if (typeof val === 'string' && val.length > 50) {
      return val.substring(0, 47) + '...';
    }
    return String(val);
  };

  const getValueColor = (val: string | number | boolean): string => {
    if (typeof val === 'boolean') {
      return val ? 'green' : 'red';
    }
    return 'white';
  };

  return (
    <Box flexDirection="column" marginBottom={1}>
      <Box justifyContent="space-between">
        <Box>
          <Text color="yellow">
            {label}:
          </Text>
          <Box marginLeft={1}>
            <Text color={getValueColor(value)}>
              {formatValue(value)}
            </Text>
          </Box>
        </Box>
        
        {editable && onEdit && (
          <Text color="blue" dimColor>
            [E] Edit
          </Text>
        )}
      </Box>
      
      {description && (
        <Box marginLeft={2}>
          <Text color="gray" dimColor>
            {description}
          </Text>
        </Box>
      )}
    </Box>
  );
}

/**
 * Config list props
 */
export interface ConfigListProps {
  title: string;
  items: string[];
  maxVisible?: number;
}

/**
 * Config list component
 */
export function ConfigList({
  title,
  items,
  maxVisible = 5
}: ConfigListProps): React.JSX.Element {
  const visibleItems = items.slice(0, maxVisible);
  const hasMore = items.length > maxVisible;

  return (
    <Box flexDirection="column" marginBottom={1}>
      <Text color="yellow">
        {title} ({items.length} items):
      </Text>
      
      <Box flexDirection="column" marginLeft={2}>
        {visibleItems.map((item, index) => (
          <Box key={index}>
            <Text color="gray">• </Text>
            <Text color="white">{item}</Text>
          </Box>
        ))}
        
        {hasMore && (
          <Text color="gray" dimColor>
            ... and {items.length - maxVisible} more
          </Text>
        )}
      </Box>
    </Box>
  );
}
