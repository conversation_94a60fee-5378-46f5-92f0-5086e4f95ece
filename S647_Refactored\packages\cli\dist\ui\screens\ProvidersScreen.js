import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useCallback, useEffect } from 'react';
import { Box, Text } from 'ink';
import { ConfigSection, ConfigItem } from '../components/ConfigSection.js';
import { HotkeyHandler, NavigationHandler } from '../components/KeyboardHandler.js';
export function ProvidersScreen({ aiManager, config, logger, onNavigate }) {
    const [state, setState] = useState({
        selectedProvider: 0,
        providerStatuses: [],
        testing: false,
    });
    // Initialize provider statuses
    useEffect(() => {
        const statuses = Object.entries(config.providers).map(([name, provider]) => ({
            name,
            enabled: provider.enabled ?? false,
            connected: false, // Will be tested
            models: [], // Will be fetched
        }));
        setState(prev => ({ ...prev, providerStatuses: statuses }));
    }, [config.providers]);
    const testProvider = useCallback(async (providerName) => {
        setState(prev => ({ ...prev, testing: true, testingProvider: providerName }));
        logger.info(`Testing connection to ${providerName}...`);
        try {
            // TODO: Implement actual provider testing
            // const provider = aiManager.getProvider(providerName);
            // const result = await provider.testConnection();
            // Simulate testing
            await new Promise(resolve => setTimeout(resolve, 1000));
            setState(prev => ({
                ...prev,
                providerStatuses: prev.providerStatuses.map(status => status.name === providerName
                    ? { ...status, connected: true, lastTested: new Date(), error: undefined }
                    : status),
                testing: false,
                testingProvider: undefined,
            }));
            logger.info(`✅ ${providerName} connection successful`);
        }
        catch (error) {
            setState(prev => ({
                ...prev,
                providerStatuses: prev.providerStatuses.map(status => status.name === providerName
                    ? {
                        ...status,
                        connected: false,
                        lastTested: new Date(),
                        error: error instanceof Error ? error.message : 'Unknown error'
                    }
                    : status),
                testing: false,
                testingProvider: undefined,
            }));
            logger.error(`❌ ${providerName} connection failed: ${error}`);
        }
    }, [aiManager, logger]);
    const testAllProviders = useCallback(async () => {
        const enabledProviders = state.providerStatuses.filter(p => p.enabled);
        for (const provider of enabledProviders) {
            await testProvider(provider.name);
        }
    }, [state.providerStatuses, testProvider]);
    const handleProviderChange = useCallback((index) => {
        setState(prev => ({ ...prev, selectedProvider: index }));
    }, []);
    const handleHotkeys = {
        't': () => {
            const selectedProvider = state.providerStatuses[state.selectedProvider];
            if (selectedProvider && !state.testing) {
                testProvider(selectedProvider.name);
            }
        },
        'a': () => {
            if (!state.testing) {
                testAllProviders();
            }
        },
        'r': () => {
            // Refresh provider list
            logger.info('Refreshing provider information...');
            setState(prev => ({
                ...prev,
                providerStatuses: prev.providerStatuses.map(status => ({
                    ...status,
                    connected: false,
                    lastTested: undefined,
                    error: undefined,
                }))
            }));
        },
        'f5': () => onNavigate('chat'),
        'f6': () => onNavigate('config'),
        'f7': () => onNavigate('providers'),
        'f8': () => onNavigate('tools'),
    };
    const getProviderStatusIcon = (status) => {
        if (!status.enabled)
            return '⚫';
        if (state.testingProvider === status.name)
            return '🔄';
        if (status.connected)
            return '🟢';
        if (status.error)
            return '🔴';
        return '🟡';
    };
    const getProviderStatusText = (status) => {
        if (!status.enabled)
            return 'Disabled';
        if (state.testingProvider === status.name)
            return 'Testing...';
        if (status.connected)
            return 'Connected';
        if (status.error)
            return 'Error';
        return 'Unknown';
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "green", bold: true, children: "\uD83D\uDD0C AI Providers Management" }) }), _jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "gray", children: "Navigate: \u2191\u2193 | Test: T | Test All: A | Refresh: R | Screens: F5-F8" }) }), _jsx(Box, { flexDirection: "column", children: state.providerStatuses.map((provider, index) => {
                    const isSelected = index === state.selectedProvider;
                    const providerConfig = config.providers[provider.name];
                    return (_jsx(Box, { marginBottom: 1, children: _jsx(ConfigSection, { title: `${provider.name.toUpperCase()} ${getProviderStatusIcon(provider)}`, collapsible: false, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(ConfigItem, { label: "Status", value: getProviderStatusText(provider), description: provider.error || 'Provider connection status' }), _jsx(ConfigItem, { label: "Type", value: providerConfig?.type || 'unknown', description: "Provider type" }), _jsx(ConfigItem, { label: "Enabled", value: provider.enabled, description: "Whether this provider is enabled" }), _jsx(ConfigItem, { label: "Timeout", value: `${providerConfig?.timeout || 30000}ms`, description: "Request timeout" }), _jsx(ConfigItem, { label: "Retries", value: providerConfig?.retries || 3, description: "Number of retry attempts" }), provider.lastTested && (_jsx(ConfigItem, { label: "Last Tested", value: provider.lastTested.toLocaleString(), description: "When this provider was last tested" })), provider.error && (_jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "red", children: ["Error: ", provider.error] }) })), isSelected && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "blue", dimColor: true, children: "Press T to test this provider" }) }))] }) }) }, provider.name));
                }) }), _jsx(Box, { marginTop: 1, borderStyle: "single", borderTop: true, padding: 1, children: _jsxs(Text, { color: "cyan", children: ["Summary: ", state.providerStatuses.filter(p => p.enabled).length, " enabled, ", ' ', state.providerStatuses.filter(p => p.connected).length, " connected, ", ' ', state.providerStatuses.filter(p => p.error).length, " errors"] }) }), _jsx(HotkeyHandler, { hotkeys: handleHotkeys, disabled: state.testing }), _jsx(NavigationHandler, { items: state.providerStatuses.map(p => p.name), selectedIndex: state.selectedProvider, onSelectionChange: handleProviderChange, disabled: state.testing })] }));
}
//# sourceMappingURL=ProvidersScreen.js.map