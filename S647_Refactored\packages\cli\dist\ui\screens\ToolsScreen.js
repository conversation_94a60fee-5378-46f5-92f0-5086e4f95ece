import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useCallback, useEffect } from 'react';
import { Box, Text } from 'ink';
import { ConfigSection, ConfigItem } from '../components/ConfigSection.js';
import { HotkeyHandler, NavigationHandler } from '../components/KeyboardHandler.js';
const TOOL_DESCRIPTIONS = {
    file: { description: 'File system operations (read, write, search)', category: 'File System' },
    git: { description: 'Git repository operations and version control', category: 'Version Control' },
    web: { description: 'Web scraping and HTTP requests', category: 'Network' },
    shell: { description: 'Shell command execution', category: 'System' },
    memory: { description: 'Memory and context management', category: 'AI' },
    search: { description: 'Search and indexing operations', category: 'Data' },
    database: { description: 'Database operations and queries', category: 'Data' },
    api: { description: 'API integration and external services', category: 'Network' },
};
export function ToolsScreen({ config, logger, onNavigate }) {
    const [state, setState] = useState({
        selectedTool: 0,
        toolStatuses: [],
        filterCategory: 'All',
        showOnlyEnabled: false,
    });
    // Initialize tool statuses
    useEffect(() => {
        const enabledTools = config.tools.enabled || [];
        const allToolNames = [
            ...enabledTools,
            ...Object.keys(TOOL_DESCRIPTIONS).filter(name => !enabledTools.includes(name))
        ];
        const statuses = allToolNames.map(name => {
            const toolInfo = TOOL_DESCRIPTIONS[name] || { description: 'Unknown tool', category: 'Other' };
            return {
                name,
                enabled: enabledTools.includes(name),
                available: true, // TODO: Check actual availability
                description: toolInfo.description,
                category: toolInfo.category,
                usageCount: 0, // TODO: Get from usage stats
            };
        });
        setState(prev => ({ ...prev, toolStatuses: statuses }));
    }, [config.tools]);
    const getFilteredTools = useCallback(() => {
        let filtered = state.toolStatuses;
        if (state.filterCategory !== 'All') {
            filtered = filtered.filter(tool => tool.category === state.filterCategory);
        }
        if (state.showOnlyEnabled) {
            filtered = filtered.filter(tool => tool.enabled);
        }
        return filtered;
    }, [state.toolStatuses, state.filterCategory, state.showOnlyEnabled]);
    const toggleTool = useCallback((toolName) => {
        setState(prev => ({
            ...prev,
            toolStatuses: prev.toolStatuses.map(tool => tool.name === toolName
                ? { ...tool, enabled: !tool.enabled }
                : tool)
        }));
        const tool = state.toolStatuses.find(t => t.name === toolName);
        if (tool) {
            logger.info(`${tool.enabled ? 'Disabled' : 'Enabled'} tool: ${toolName}`);
        }
    }, [state.toolStatuses, logger]);
    const testTool = useCallback(async (toolName) => {
        logger.info(`Testing tool: ${toolName}...`);
        try {
            // TODO: Implement actual tool testing
            // Simulate testing
            await new Promise(resolve => setTimeout(resolve, 500));
            setState(prev => ({
                ...prev,
                toolStatuses: prev.toolStatuses.map(tool => tool.name === toolName
                    ? { ...tool, available: true, error: undefined, lastUsed: new Date() }
                    : tool)
            }));
            logger.info(`✅ Tool ${toolName} test successful`);
        }
        catch (error) {
            setState(prev => ({
                ...prev,
                toolStatuses: prev.toolStatuses.map(tool => tool.name === toolName
                    ? {
                        ...tool,
                        available: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                    }
                    : tool)
            }));
            logger.error(`❌ Tool ${toolName} test failed: ${error}`);
        }
    }, [logger]);
    const handleToolChange = useCallback((index) => {
        setState(prev => ({ ...prev, selectedTool: index }));
    }, []);
    const cycleFilter = useCallback(() => {
        const categories = ['All', ...new Set(state.toolStatuses.map(t => t.category))];
        const currentIndex = categories.indexOf(state.filterCategory);
        const nextIndex = (currentIndex + 1) % categories.length;
        setState(prev => ({ ...prev, filterCategory: categories[nextIndex] || 'All' }));
    }, [state.filterCategory, state.toolStatuses]);
    const handleHotkeys = {
        't': () => {
            const filteredTools = getFilteredTools();
            const selectedTool = filteredTools[state.selectedTool];
            if (selectedTool) {
                testTool(selectedTool.name);
            }
        },
        'e': () => {
            const filteredTools = getFilteredTools();
            const selectedTool = filteredTools[state.selectedTool];
            if (selectedTool) {
                toggleTool(selectedTool.name);
            }
        },
        'f': () => {
            cycleFilter();
        },
        'o': () => {
            setState(prev => ({ ...prev, showOnlyEnabled: !prev.showOnlyEnabled }));
        },
        'f5': () => onNavigate('chat'),
        'f6': () => onNavigate('config'),
        'f7': () => onNavigate('providers'),
        'f8': () => onNavigate('tools'),
    };
    const getToolStatusIcon = (tool) => {
        if (!tool.enabled)
            return '⚫';
        if (!tool.available)
            return '🔴';
        if (tool.error)
            return '🟡';
        return '🟢';
    };
    const getToolStatusText = (tool) => {
        if (!tool.enabled)
            return 'Disabled';
        if (!tool.available)
            return 'Unavailable';
        if (tool.error)
            return 'Error';
        return 'Ready';
    };
    const filteredTools = getFilteredTools();
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "magenta", bold: true, children: "\uD83D\uDEE0\uFE0F Tools Management" }) }), _jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "gray", children: "Navigate: \u2191\u2193 | Toggle: E | Test: T | Filter: F | Show Enabled: O | Screens: F5-F8" }) }), _jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: "cyan", children: ["Filter: ", state.filterCategory, " | Show Only Enabled: ", state.showOnlyEnabled ? 'Yes' : 'No', " | Total: ", filteredTools.length, "/", state.toolStatuses.length] }) }), _jsx(Box, { flexDirection: "column", children: filteredTools.map((tool, index) => {
                    const isSelected = index === state.selectedTool;
                    const toolConfig = config.tools[tool.name];
                    return (_jsx(Box, { marginBottom: 1, children: _jsx(ConfigSection, { title: `${tool.name.toUpperCase()} ${getToolStatusIcon(tool)} [${tool.category}]`, collapsible: false, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "gray", children: tool.description }) }), _jsx(ConfigItem, { label: "Status", value: getToolStatusText(tool), description: tool.error || 'Tool availability status' }), _jsx(ConfigItem, { label: "Enabled", value: tool.enabled, description: "Whether this tool is enabled" }), _jsx(ConfigItem, { label: "Category", value: tool.category, description: "Tool category" }), typeof toolConfig === 'object' && toolConfig && (_jsxs(_Fragment, { children: [_jsx(ConfigItem, { label: "Timeout", value: `${toolConfig.timeout || 'N/A'}ms`, description: "Tool timeout" }), _jsx(ConfigItem, { label: "Retries", value: toolConfig.retries || 'N/A', description: "Number of retry attempts" })] })), tool.usageCount !== undefined && (_jsx(ConfigItem, { label: "Usage Count", value: tool.usageCount, description: "Number of times this tool has been used" })), tool.lastUsed && (_jsx(ConfigItem, { label: "Last Used", value: tool.lastUsed.toLocaleString(), description: "When this tool was last used" })), tool.error && (_jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "red", children: ["Error: ", tool.error] }) })), isSelected && (_jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "blue", dimColor: true, children: ["Press E to ", tool.enabled ? 'disable' : 'enable', " | Press T to test"] }) }))] }) }) }, tool.name));
                }) }), _jsx(Box, { marginTop: 1, borderStyle: "single", borderTop: true, padding: 1, children: _jsxs(Text, { color: "cyan", children: ["Summary: ", state.toolStatuses.filter(t => t.enabled).length, " enabled, ", ' ', state.toolStatuses.filter(t => t.available).length, " available, ", ' ', state.toolStatuses.filter(t => t.error).length, " errors"] }) }), _jsx(HotkeyHandler, { hotkeys: handleHotkeys }), _jsx(NavigationHandler, { items: filteredTools.map(t => t.name), selectedIndex: state.selectedTool, onSelectionChange: handleToolChange })] }));
}
//# sourceMappingURL=ToolsScreen.js.map