/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
/**
 * Keyboard handler props
 */
export interface KeyboardHandlerProps {
    onKeyPress?: (input: string, key: any) => void;
    onArrowUp?: () => void;
    onArrowDown?: () => void;
    onArrowLeft?: () => void;
    onArrowRight?: () => void;
    onEnter?: () => void;
    onEscape?: () => void;
    onTab?: () => void;
    onBackspace?: () => void;
    onDelete?: () => void;
    onHome?: () => void;
    onEnd?: () => void;
    onPageUp?: () => void;
    onPageDown?: () => void;
    disabled?: boolean;
}
/**
 * Keyboard handler component
 */
export declare function KeyboardHandler({ onKeyPress, onArrowUp, onArrowDown, onArrowLeft, onArrowRight, onEnter, onEscape, onTab, onBackspace, onDelete, onHome, onEnd, onPageUp, onPageDown, disabled }: KeyboardHandlerProps): React.JSX.Element | null;
/**
 * Navigation handler props
 */
export interface NavigationHandlerProps {
    items: string[];
    selectedIndex: number;
    onSelectionChange: (index: number) => void;
    onSelect?: (index: number, item: string) => void;
    disabled?: boolean;
    wrap?: boolean;
}
/**
 * Navigation handler component for lists
 */
export declare function NavigationHandler({ items, selectedIndex, onSelectionChange, onSelect, disabled, wrap }: NavigationHandlerProps): React.JSX.Element | null;
/**
 * Hotkey handler props
 */
export interface HotkeyHandlerProps {
    hotkeys: Record<string, () => void>;
    disabled?: boolean;
}
/**
 * Hotkey handler component
 */
export declare function HotkeyHandler({ hotkeys, disabled }: HotkeyHandlerProps): React.JSX.Element | null;
//# sourceMappingURL=KeyboardHandler.d.ts.map