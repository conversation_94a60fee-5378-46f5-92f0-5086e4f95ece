/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import type { AIManager } from '@inkbytefo/s647-core-refactored';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { Screen } from '../App.js';
interface ProvidersScreenProps {
    aiManager: AIManager;
    config: Configuration;
    logger: Logger;
    onNavigate: (screen: Screen) => void;
}
export declare function ProvidersScreen({ aiManager, config, logger, onNavigate }: ProvidersScreenProps): React.JSX.Element;
export {};
//# sourceMappingURL=ProvidersScreen.d.ts.map