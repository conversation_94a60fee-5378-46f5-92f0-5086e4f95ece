import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { Box, Text } from 'ink';
/**
 * Config section component
 */
export function ConfigSection({ title, children, collapsible = false, collapsed = false, onToggle }) {
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Box, { marginBottom: 1, children: [_jsxs(Text, { color: "cyan", bold: true, children: [collapsible ? (collapsed ? '▶ ' : '▼ ') : '● ', title] }), collapsible && onToggle && (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["(Press Enter to ", collapsed ? 'expand' : 'collapse', ")"] }) }))] }), !collapsed && (_jsx(Box, { flexDirection: "column", marginLeft: 2, children: children }))] }));
}
/**
 * Config item component
 */
export function ConfigItem({ label, value, description, editable = false, onEdit }) {
    const formatValue = (val) => {
        if (typeof val === 'boolean') {
            return val ? '✅ Enabled' : '❌ Disabled';
        }
        if (typeof val === 'string' && val.length > 50) {
            return val.substring(0, 47) + '...';
        }
        return String(val);
    };
    const getValueColor = (val) => {
        if (typeof val === 'boolean') {
            return val ? 'green' : 'red';
        }
        return 'white';
    };
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Box, { justifyContent: "space-between", children: [_jsxs(Box, { children: [_jsxs(Text, { color: "yellow", children: [label, ":"] }), _jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: getValueColor(value), children: formatValue(value) }) })] }), editable && onEdit && (_jsx(Text, { color: "blue", dimColor: true, children: "[E] Edit" }))] }), description && (_jsx(Box, { marginLeft: 2, children: _jsx(Text, { color: "gray", dimColor: true, children: description }) }))] }));
}
/**
 * Config list component
 */
export function ConfigList({ title, items, maxVisible = 5 }) {
    const visibleItems = items.slice(0, maxVisible);
    const hasMore = items.length > maxVisible;
    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Text, { color: "yellow", children: [title, " (", items.length, " items):"] }), _jsxs(Box, { flexDirection: "column", marginLeft: 2, children: [visibleItems.map((item, index) => (_jsxs(Box, { children: [_jsx(Text, { color: "gray", children: "\u2022 " }), _jsx(Text, { color: "white", children: item })] }, index))), hasMore && (_jsxs(Text, { color: "gray", dimColor: true, children: ["... and ", items.length - maxVisible, " more"] }))] })] }));
}
//# sourceMappingURL=ConfigSection.js.map