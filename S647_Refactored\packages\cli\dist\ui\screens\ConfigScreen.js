import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useCallback } from 'react';
import { Box, Text } from 'ink';
import { ConfigSection, ConfigItem, ConfigList } from '../components/ConfigSection.js';
import { HotkeyHandler, NavigationHandler } from '../components/KeyboardHandler.js';
export function ConfigScreen({ config, logger, onNavigate }) {
    const [state, setState] = useState({
        selectedSection: 0,
        collapsedSections: {},
        editMode: false,
    });
    // Configuration sections
    const sections = [
        'general',
        'providers',
        'tools',
        'ui',
        'logging',
        'telemetry',
        'security',
        'performance'
    ];
    const handleSectionChange = useCallback((index) => {
        setState(prev => ({ ...prev, selectedSection: index }));
    }, []);
    const toggleSection = useCallback((sectionName) => {
        setState(prev => ({
            ...prev,
            collapsedSections: {
                ...prev.collapsedSections,
                [sectionName]: !prev.collapsedSections[sectionName]
            }
        }));
    }, []);
    const handleHotkeys = {
        'r': () => {
            logger.info('Reloading configuration...');
            // TODO: Implement config reload
        },
        's': () => {
            logger.info('Saving configuration...');
            // TODO: Implement config save
        },
        'e': () => {
            setState(prev => ({ ...prev, editMode: !prev.editMode }));
        },
        'f5': () => onNavigate('chat'),
        'f6': () => onNavigate('config'),
        'f7': () => onNavigate('providers'),
        'f8': () => onNavigate('tools'),
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "yellow", bold: true, children: "\u2699\uFE0F Configuration Management" }) }), _jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "gray", children: "Navigate: \u2191\u2193 | Toggle: Enter | Edit: E | Reload: R | Save: S | Screens: F5-F8" }) }), _jsxs(Box, { flexDirection: "column", children: [_jsxs(ConfigSection, { title: "General Configuration", collapsible: true, collapsed: state.collapsedSections.general ?? false, onToggle: () => toggleSection('general'), children: [_jsx(ConfigItem, { label: "Version", value: config.version, description: "Current configuration version" }), _jsx(ConfigItem, { label: "Environment", value: config.environment, description: "Runtime environment (development, production, test)", editable: state.editMode }), _jsx(ConfigItem, { label: "Default Provider", value: config.defaultProvider, description: "Default AI provider to use", editable: state.editMode })] }), _jsx(ConfigSection, { title: "AI Providers", collapsible: true, collapsed: state.collapsedSections.providers ?? false, onToggle: () => toggleSection('providers'), children: Object.entries(config.providers).map(([name, provider]) => (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Text, { color: "cyan", children: name.toUpperCase() }), _jsxs(Box, { marginLeft: 2, flexDirection: "column", children: [_jsx(ConfigItem, { label: "Type", value: provider.type, description: `Provider type: ${provider.type}` }), _jsx(ConfigItem, { label: "Enabled", value: provider.enabled ?? false, description: "Whether this provider is enabled", editable: state.editMode }), _jsx(ConfigItem, { label: "Timeout", value: `${provider.timeout ?? 30000}ms`, description: "Request timeout in milliseconds", editable: state.editMode }), _jsx(ConfigItem, { label: "Retries", value: provider.retries ?? 3, description: "Number of retry attempts", editable: state.editMode })] })] }, name))) }), _jsxs(ConfigSection, { title: "Tools Configuration", collapsible: true, collapsed: state.collapsedSections.tools ?? false, onToggle: () => toggleSection('tools'), children: [_jsx(ConfigItem, { label: "Enabled Tools", value: config.tools.enabled.join(', '), description: "Currently enabled tools", editable: state.editMode }), Object.entries(config.tools).filter(([key]) => key !== 'enabled').map(([name, toolConfig]) => (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Text, { color: "cyan", children: name.toUpperCase() }), _jsx(Box, { marginLeft: 2, flexDirection: "column", children: typeof toolConfig === 'object' && toolConfig && (_jsxs(_Fragment, { children: [_jsx(ConfigItem, { label: "Enabled", value: toolConfig.enabled ?? true, description: `Whether ${name} tool is enabled`, editable: state.editMode }), _jsx(ConfigItem, { label: "Timeout", value: `${toolConfig.timeout ?? 'N/A'}ms`, description: "Tool timeout in milliseconds", editable: state.editMode })] })) })] }, name)))] }), _jsxs(ConfigSection, { title: "User Interface", collapsible: true, collapsed: state.collapsedSections.ui ?? false, onToggle: () => toggleSection('ui'), children: [_jsx(ConfigItem, { label: "Theme", value: config.ui.theme, description: "UI color theme", editable: state.editMode }), _jsx(ConfigItem, { label: "Animations", value: config.ui.animations, description: "Enable UI animations", editable: state.editMode }), _jsx(ConfigItem, { label: "Verbose Mode", value: config.ui.verbose, description: "Show detailed information", editable: state.editMode }), _jsx(ConfigItem, { label: "Show Timestamps", value: config.ui.showTimestamps, description: "Display timestamps in messages", editable: state.editMode }), _jsx(ConfigItem, { label: "Auto Scroll", value: config.ui.autoScroll, description: "Automatically scroll to new content", editable: state.editMode })] }), _jsxs(ConfigSection, { title: "Logging", collapsible: true, collapsed: state.collapsedSections.logging ?? false, onToggle: () => toggleSection('logging'), children: [_jsx(ConfigItem, { label: "Log Level", value: config.logging.level, description: "Minimum log level to display", editable: state.editMode }), _jsx(ConfigItem, { label: "Log Format", value: config.logging.format, description: "Log output format", editable: state.editMode }), _jsx(ConfigItem, { label: "Log Output", value: config.logging.output, description: "Where to output logs", editable: state.editMode }), _jsx(ConfigItem, { label: "Include Timestamp", value: config.logging.includeTimestamp, description: "Include timestamps in log entries", editable: state.editMode })] }), _jsxs(ConfigSection, { title: "Telemetry & Analytics", collapsible: true, collapsed: state.collapsedSections.telemetry ?? false, onToggle: () => toggleSection('telemetry'), children: [_jsx(ConfigItem, { label: "Telemetry Enabled", value: config.telemetry.enabled, description: "Enable anonymous usage analytics", editable: state.editMode }), _jsx(ConfigItem, { label: "Collect Usage", value: config.telemetry.collectUsage, description: "Collect usage statistics", editable: state.editMode }), _jsx(ConfigItem, { label: "Collect Errors", value: config.telemetry.collectErrors, description: "Collect error reports", editable: state.editMode }), _jsx(ConfigItem, { label: "Anonymize Data", value: config.telemetry.anonymize, description: "Anonymize collected data", editable: state.editMode })] })] }), _jsx(HotkeyHandler, { hotkeys: handleHotkeys }), _jsx(NavigationHandler, { items: sections, selectedIndex: state.selectedSection, onSelectionChange: handleSectionChange })] }));
}
//# sourceMappingURL=ConfigScreen.js.map