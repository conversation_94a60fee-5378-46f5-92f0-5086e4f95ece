/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
/**
 * Config section props
 */
export interface ConfigSectionProps {
    title: string;
    children: React.ReactNode;
    collapsible?: boolean;
    collapsed?: boolean;
    onToggle?: () => void;
}
/**
 * Config section component
 */
export declare function ConfigSection({ title, children, collapsible, collapsed, onToggle }: ConfigSectionProps): React.JSX.Element;
/**
 * Config item props
 */
export interface ConfigItemProps {
    label: string;
    value: string | number | boolean;
    description?: string;
    editable?: boolean;
    onEdit?: () => void;
}
/**
 * Config item component
 */
export declare function ConfigItem({ label, value, description, editable, onEdit }: ConfigItemProps): React.JSX.Element;
/**
 * Config list props
 */
export interface ConfigListProps {
    title: string;
    items: string[];
    maxVisible?: number;
}
/**
 * Config list component
 */
export declare function ConfigList({ title, items, maxVisible }: ConfigListProps): React.JSX.Element;
//# sourceMappingURL=ConfigSection.d.ts.map