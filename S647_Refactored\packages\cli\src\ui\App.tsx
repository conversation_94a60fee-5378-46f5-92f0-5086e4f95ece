/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useApp, useInput } from 'ink';
import type { CliArgs } from '../config/args.js';
import type { Configuration } from '../config/types.js';
import type { Logger } from '@inkbytefo/s647-shared';
import { ChatScreen } from './screens/ChatScreen.js';
import { ConfigScreen } from './screens/ConfigScreen.js';
import { ProvidersScreen } from './screens/ProvidersScreen.js';
import { ToolsScreen } from './screens/ToolsScreen.js';
import { ErrorBoundary } from './components/ErrorBoundary.js';
import { LoadingSpinner } from './components/LoadingSpinner.js';
import { StatusBar } from './components/StatusBar.js';
import { AIManager } from '@inkbytefo/s647-core-refactored';

/**
 * Screen types
 */
export type Screen = 'chat' | 'config' | 'providers' | 'tools';

/**
 * App props
 */
export interface AppProps {
  args: CliArgs;
  config: Configuration;
  logger: Logger;
}

/**
 * App state
 */
interface AppState {
  currentScreen: Screen;
  aiManager?: AIManager;
  isLoading: boolean;
  error?: Error;
}

/**
 * Main App component
 */
export function App({ args, config, logger }: AppProps): React.JSX.Element {
  const { exit } = useApp();
  const [state, setState] = useState<AppState>({
    currentScreen: 'chat',
    isLoading: true,
  });

  // Initialize AI Manager
  useEffect(() => {
    const initializeAI = async () => {
      try {
        const aiManager = new AIManager(logger);
        const result = await aiManager.initialize(
          config.providers,
          config.defaultProvider
        );

        if (!result.success) {
          throw result.error;
        }

        setState(prev => ({
          ...prev,
          aiManager,
          isLoading: false,
        }));

        logger.info('AI Manager initialized successfully');
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        logger.error('Failed to initialize AI Manager', { error: err.message });
        
        setState(prev => ({
          ...prev,
          error: err,
          isLoading: false,
        }));
      }
    };

    initializeAI();
  }, [config, logger]);

  // Handle keyboard shortcuts for navigation
  useInput((input, key) => {
    // Handle Ctrl+C for exit
    if (key.ctrl && input === 'c') {
      exit();
      return;
    }

    // Function keys for screen navigation (using meta key combinations)
    if (key.meta && input === '1') {
      setState(prev => ({ ...prev, currentScreen: 'chat' }));
    } else if (key.meta && input === '2') {
      setState(prev => ({ ...prev, currentScreen: 'config' }));
    } else if (key.meta && input === '3') {
      setState(prev => ({ ...prev, currentScreen: 'providers' }));
    } else if (key.meta && input === '4') {
      setState(prev => ({ ...prev, currentScreen: 'tools' }));
    }

    // Number keys for quick navigation
    if (input === '1') {
      setState(prev => ({ ...prev, currentScreen: 'chat' }));
    } else if (input === '2') {
      setState(prev => ({ ...prev, currentScreen: 'config' }));
    } else if (input === '3') {
      setState(prev => ({ ...prev, currentScreen: 'providers' }));
    } else if (input === '4') {
      setState(prev => ({ ...prev, currentScreen: 'tools' }));
    }
  }, { isActive: !state.isLoading });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (state.aiManager) {
        state.aiManager.dispose();
      }
    };
  }, [state.aiManager]);

  // Handle navigation
  const navigateToScreen = (screen: Screen) => {
    setState(prev => ({ ...prev, currentScreen: screen }));
  };

  // Render loading state
  if (state.isLoading) {
    return (
      <Box flexDirection="column" height="100%">
        <Box flexGrow={1} justifyContent="center" alignItems="center">
          <LoadingSpinner text="Initializing S647..." />
        </Box>
        <StatusBar 
          status="loading" 
          message="Setting up AI providers..." 
        />
      </Box>
    );
  }

  // Render error state
  if (state.error) {
    return (
      <Box flexDirection="column" height="100%">
        <Box flexGrow={1} justifyContent="center" alignItems="center">
          <Box flexDirection="column" alignItems="center">
            <Text color="red" bold>
              ❌ Failed to initialize S647
            </Text>
            <Text color="red">
              {state.error.message}
            </Text>
            <Box marginTop={1}>
              <Text color="gray">
                Press Ctrl+C to exit
              </Text>
            </Box>
          </Box>
        </Box>
        <StatusBar 
          status="error" 
          message={state.error.message} 
        />
      </Box>
    );
  }

  // Render main app
  return (
    <ErrorBoundary>
      <Box flexDirection="column" height="100%">
        <Box flexGrow={1}>
          {state.currentScreen === 'chat' && (
            <ChatScreen
              aiManager={state.aiManager!}
              config={config}
              logger={logger}
              onNavigate={navigateToScreen}
            />
          )}
          {state.currentScreen === 'config' && (
            <ConfigScreen
              config={config}
              logger={logger}
              onNavigate={navigateToScreen}
            />
          )}
          {state.currentScreen === 'providers' && (
            <ProvidersScreen
              aiManager={state.aiManager!}
              config={config}
              logger={logger}
              onNavigate={navigateToScreen}
            />
          )}
          {state.currentScreen === 'tools' && (
            <ToolsScreen
              config={config}
              logger={logger}
              onNavigate={navigateToScreen}
            />
          )}
        </Box>
        
        <StatusBar
          status="ready"
          message={`Screen: ${state.currentScreen} | 1: Chat | 2: Config | 3: Providers | 4: Tools | Alt+1-4: Quick Nav | Ctrl+C: Exit`}
        />
      </Box>
    </ErrorBoundary>
  );
}
