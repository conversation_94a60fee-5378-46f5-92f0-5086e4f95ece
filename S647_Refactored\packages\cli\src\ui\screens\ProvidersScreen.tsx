/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Box, Text } from 'ink';
import type { AIManager } from '@inkbytefo/s647-core-refactored';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { Screen } from '../App.js';
import { ConfigSection, ConfigItem } from '../components/ConfigSection.js';
import { HotkeyHandler, NavigationHandler } from '../components/KeyboardHandler.js';

interface ProvidersScreenProps {
  aiManager: AIManager;
  config: Configuration;
  logger: Logger;
  onNavigate: (screen: Screen) => void;
}

interface ProviderStatus {
  name: string;
  enabled: boolean;
  connected: boolean;
  lastTested?: Date | undefined;
  error?: string | undefined;
  models?: string[] | undefined;
}

interface ProvidersScreenState {
  selectedProvider: number;
  providerStatuses: ProviderStatus[];
  testing: boolean;
  testingProvider?: string | undefined;
}

export function ProvidersScreen({ aiManager, config, logger, onNavigate }: ProvidersScreenProps): React.JSX.Element {
  const [state, setState] = useState<ProvidersScreenState>({
    selectedProvider: 0,
    providerStatuses: [],
    testing: false,
  });

  // Initialize provider statuses
  useEffect(() => {
    const statuses: ProviderStatus[] = Object.entries(config.providers).map(([name, provider]) => ({
      name,
      enabled: provider.enabled ?? false,
      connected: false, // Will be tested
      models: [], // Will be fetched
    }));

    setState(prev => ({ ...prev, providerStatuses: statuses }));
  }, [config.providers]);

  const testProvider = useCallback(async (providerName: string) => {
    setState(prev => ({ ...prev, testing: true, testingProvider: providerName }));
    logger.info(`Testing connection to ${providerName}...`);

    try {
      // TODO: Implement actual provider testing
      // const provider = aiManager.getProvider(providerName);
      // const result = await provider.testConnection();

      // Simulate testing
      await new Promise(resolve => setTimeout(resolve, 1000));

      setState(prev => ({
        ...prev,
        providerStatuses: prev.providerStatuses.map(status =>
          status.name === providerName
            ? { ...status, connected: true, lastTested: new Date(), error: undefined }
            : status
        ),
        testing: false,
        testingProvider: undefined,
      }));

      logger.info(`✅ ${providerName} connection successful`);
    } catch (error) {
      setState(prev => ({
        ...prev,
        providerStatuses: prev.providerStatuses.map(status =>
          status.name === providerName
            ? {
                ...status,
                connected: false,
                lastTested: new Date(),
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            : status
        ),
        testing: false,
        testingProvider: undefined,
      }));

      logger.error(`❌ ${providerName} connection failed: ${error}`);
    }
  }, [aiManager, logger]);

  const testAllProviders = useCallback(async () => {
    const enabledProviders = state.providerStatuses.filter(p => p.enabled);

    for (const provider of enabledProviders) {
      await testProvider(provider.name);
    }
  }, [state.providerStatuses, testProvider]);

  const handleProviderChange = useCallback((index: number) => {
    setState(prev => ({ ...prev, selectedProvider: index }));
  }, []);

  const handleHotkeys = {
    't': () => {
      const selectedProvider = state.providerStatuses[state.selectedProvider];
      if (selectedProvider && !state.testing) {
        testProvider(selectedProvider.name);
      }
    },
    'a': () => {
      if (!state.testing) {
        testAllProviders();
      }
    },
    'r': () => {
      // Refresh provider list
      logger.info('Refreshing provider information...');
      setState(prev => ({
        ...prev,
        providerStatuses: prev.providerStatuses.map(status => ({
          ...status,
          connected: false,
          lastTested: undefined,
          error: undefined,
        }))
      }));
    },
    'f5': () => onNavigate('chat'),
    'f6': () => onNavigate('config'),
    'f7': () => onNavigate('providers'),
    'f8': () => onNavigate('tools'),
  };

  const getProviderStatusIcon = (status: ProviderStatus): string => {
    if (!status.enabled) return '⚫';
    if (state.testingProvider === status.name) return '🔄';
    if (status.connected) return '🟢';
    if (status.error) return '🔴';
    return '🟡';
  };

  const getProviderStatusText = (status: ProviderStatus): string => {
    if (!status.enabled) return 'Disabled';
    if (state.testingProvider === status.name) return 'Testing...';
    if (status.connected) return 'Connected';
    if (status.error) return 'Error';
    return 'Unknown';
  };

  return (
    <Box flexDirection="column" padding={1}>
      {/* Header */}
      <Box marginBottom={1}>
        <Text color="green" bold>
          🔌 AI Providers Management
        </Text>
      </Box>

      {/* Help text */}
      <Box marginBottom={1}>
        <Text color="gray">
          Navigate: ↑↓ | Test: T | Test All: A | Refresh: R | Screens: F5-F8
        </Text>
      </Box>

      {/* Provider list */}
      <Box flexDirection="column">
        {state.providerStatuses.map((provider, index) => {
          const isSelected = index === state.selectedProvider;
          const providerConfig = config.providers[provider.name];

          return (
            <Box key={provider.name} marginBottom={1}>
              <ConfigSection
                title={`${provider.name.toUpperCase()} ${getProviderStatusIcon(provider)}`}
                collapsible={false}
              >
                <Box flexDirection="column">
                  {/* Status */}
                  <ConfigItem
                    label="Status"
                    value={getProviderStatusText(provider)}
                    description={provider.error || 'Provider connection status'}
                  />

                  {/* Configuration */}
                  <ConfigItem
                    label="Type"
                    value={providerConfig?.type || 'unknown'}
                    description="Provider type"
                  />

                  <ConfigItem
                    label="Enabled"
                    value={provider.enabled}
                    description="Whether this provider is enabled"
                  />

                  <ConfigItem
                    label="Timeout"
                    value={`${providerConfig?.timeout || 30000}ms`}
                    description="Request timeout"
                  />

                  <ConfigItem
                    label="Retries"
                    value={providerConfig?.retries || 3}
                    description="Number of retry attempts"
                  />

                  {/* Last tested */}
                  {provider.lastTested && (
                    <ConfigItem
                      label="Last Tested"
                      value={provider.lastTested.toLocaleString()}
                      description="When this provider was last tested"
                    />
                  )}

                  {/* Error details */}
                  {provider.error && (
                    <Box marginTop={1}>
                      <Text color="red">
                        Error: {provider.error}
                      </Text>
                    </Box>
                  )}

                  {/* Test button indicator */}
                  {isSelected && (
                    <Box marginTop={1}>
                      <Text color="blue" dimColor>
                        Press T to test this provider
                      </Text>
                    </Box>
                  )}
                </Box>
              </ConfigSection>
            </Box>
          );
        })}
      </Box>

      {/* Summary */}
      <Box marginTop={1} borderStyle="single" borderTop padding={1}>
        <Text color="cyan">
          Summary: {state.providerStatuses.filter(p => p.enabled).length} enabled, {' '}
          {state.providerStatuses.filter(p => p.connected).length} connected, {' '}
          {state.providerStatuses.filter(p => p.error).length} errors
        </Text>
      </Box>

      {/* Keyboard handlers */}
      <HotkeyHandler hotkeys={handleHotkeys} disabled={state.testing} />
      <NavigationHandler
        items={state.providerStatuses.map(p => p.name)}
        selectedIndex={state.selectedProvider}
        onSelectionChange={handleProviderChange}
        disabled={state.testing}
      />
    </Box>
  );
}
