/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import type { AIManager } from '@inkbytefo/s647-core-refactored';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { Screen } from '../App.js';
import { HotkeyHandler } from '../components/KeyboardHandler.js';

interface ChatScreenProps {
  aiManager: AIManager;
  config: Configuration;
  logger: Logger;
  onNavigate: (screen: Screen) => void;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  provider?: string;
}

interface ChatScreenState {
  messages: ChatMessage[];
  currentProvider: string;
  isTyping: boolean;
  inputBuffer: string;
  showProviderSelector: boolean;
}

export function ChatScreen({ aiManager, config, logger, onNavigate }: ChatScreenProps): React.JSX.Element {
  const [state, setState] = useState<ChatScreenState>({
    messages: [
      {
        id: '1',
        role: 'system',
        content: 'Welcome to S647 AI Chat! Select a provider and start chatting.',
        timestamp: new Date(),
      }
    ],
    currentProvider: config.defaultProvider,
    isTyping: false,
    inputBuffer: '',
    showProviderSelector: false,
  });

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      isTyping: true,
      inputBuffer: '',
    }));

    try {
      logger.info(`Sending message to ${state.currentProvider}...`);

      // TODO: Implement actual AI provider communication
      // Simulate AI response
      await new Promise(resolve => setTimeout(resolve, 1000));

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `This is a simulated response from ${state.currentProvider}. The actual AI integration will be implemented in future versions.`,
        timestamp: new Date(),
        provider: state.currentProvider,
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, assistantMessage],
        isTyping: false,
      }));

      logger.info('Message sent successfully');
    } catch (error) {
      logger.error(`Failed to send message: ${error}`);

      const errorMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        role: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, errorMessage],
        isTyping: false,
      }));
    }
  }, [aiManager, logger, state.currentProvider]);

  const handleInput = useCallback((input: string, key: any) => {
    if (key.return) {
      if (state.inputBuffer.trim()) {
        sendMessage(state.inputBuffer);
      }
    } else if (key.backspace) {
      setState(prev => ({
        ...prev,
        inputBuffer: prev.inputBuffer.slice(0, -1),
      }));
    } else if (input && !key.ctrl && !key.meta) {
      setState(prev => ({
        ...prev,
        inputBuffer: prev.inputBuffer + input,
      }));
    }
  }, [state.inputBuffer, sendMessage]);

  const handleHotkeys = {
    'p': () => {
      setState(prev => ({ ...prev, showProviderSelector: !prev.showProviderSelector }));
    },
    'c': () => {
      setState(prev => ({ ...prev, messages: [], inputBuffer: '' }));
      logger.info('Chat cleared');
    },
    'f5': () => onNavigate('chat'),
    'f6': () => onNavigate('config'),
    'f7': () => onNavigate('providers'),
    'f8': () => onNavigate('tools'),
  };

  // Use input hook for chat input
  useInput(handleInput, { isActive: !state.showProviderSelector });

  const availableProviders = Object.keys(config.providers).filter(
    name => config.providers[name]?.enabled
  );

  const getMessageColor = (role: ChatMessage['role']): string => {
    switch (role) {
      case 'user': return 'blue';
      case 'assistant': return 'green';
      case 'system': return 'yellow';
      default: return 'white';
    }
  };

  const getMessagePrefix = (role: ChatMessage['role']): string => {
    switch (role) {
      case 'user': return '👤 You';
      case 'assistant': return '🤖 AI';
      case 'system': return '⚙️ System';
      default: return '❓';
    }
  };

  return (
    <Box flexDirection="column" padding={1} height="100%">
      {/* Header */}
      <Box marginBottom={1} justifyContent="space-between">
        <Text color="cyan" bold>
          💬 AI Chat Interface
        </Text>
        <Text color="cyan">
          Provider: {state.currentProvider}
        </Text>
      </Box>

      {/* Help text */}
      <Box marginBottom={1}>
        <Text color="gray">
          Type your message and press Enter | Clear: C | Switch Provider: P | Screens: F5-F8
        </Text>
      </Box>

      {/* Provider selector */}
      {state.showProviderSelector && (
        <Box marginBottom={1} borderStyle="single" padding={1}>
          <Box flexDirection="column">
            <Text color="cyan" bold>Select Provider:</Text>
            {availableProviders.map((provider, index) => (
              <Box key={provider} marginLeft={2}>
                <Text color={provider === state.currentProvider ? 'green' : 'white'}>
                  {index + 1}. {provider} {provider === state.currentProvider ? '(current)' : ''}
                </Text>
              </Box>
            ))}
            <Box marginTop={1}>
              <Text color="gray" dimColor>
                Press P to close
              </Text>
            </Box>
          </Box>
        </Box>
      )}

      {/* Messages */}
      <Box flexDirection="column" flexGrow={1} marginBottom={1}>
        {state.messages.map((message) => (
          <Box key={message.id} marginBottom={1}>
            <Box flexDirection="column">
              <Box>
                <Text color={getMessageColor(message.role)} bold>
                  {getMessagePrefix(message.role)}
                </Text>
                <Box marginLeft={1}>
                  <Text color="gray" dimColor>
                    {message.timestamp.toLocaleTimeString()}
                  </Text>
                </Box>
                {message.provider && (
                  <Box marginLeft={1}>
                    <Text color="gray" dimColor>
                      via {message.provider}
                    </Text>
                  </Box>
                )}
              </Box>
              <Box marginLeft={2} marginTop={1}>
                <Text color="white">
                  {message.content}
                </Text>
              </Box>
            </Box>
          </Box>
        ))}

        {state.isTyping && (
          <Box marginBottom={1}>
            <Text color="green" bold>
              🤖 AI is typing...
            </Text>
          </Box>
        )}
      </Box>

      {/* Input area */}
      <Box borderStyle="single" borderTop padding={1}>
        <Box>
          <Text color="blue">
            💬 Message:
          </Text>
          <Box marginLeft={1}>
            <Text color="white">
              {state.inputBuffer}
              <Text color="gray">█</Text>
            </Text>
          </Box>
        </Box>
      </Box>

      {/* Status */}
      <Box marginTop={1}>
        <Text color="cyan">
          Messages: {state.messages.length} | Provider: {state.currentProvider} |
          Status: {state.isTyping ? 'Processing...' : 'Ready'}
        </Text>
      </Box>

      {/* Keyboard handlers */}
      <HotkeyHandler hotkeys={handleHotkeys} disabled={state.isTyping} />
    </Box>
  );
}
