{"version": 3, "file": "loader.js", "sourceRoot": "", "sources": ["../../src/config/loader.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,oBAAoB,EAAE,MAAM,iCAAiC,CAAC;AAGvE;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,IAAa;IACnD,MAAM,aAAa,GAAG,IAAI,oBAAoB,EAAE,CAAC;IAEjD,uCAAuC;IACvC,MAAM,aAAa,GAAG;QACpB,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE;QACtB,SAAS,EAAE,OAAO;QAClB,IAAI,EAAE,IAA2B;KAClC,CAAC;IAEF,sCAAsC;IACtC,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAEvD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACpB,sBAAsB;QACtB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,gCAAgC;QAChC,MAAM,MAAM,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IAClE,CAAC;IAED,sBAAsB;IACtB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QACpB,OAAO,CAAC,IAAI,CAAC,yBAAyB,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,MAAM,CAAC,IAAI,CAAC;AACrB,CAAC"}