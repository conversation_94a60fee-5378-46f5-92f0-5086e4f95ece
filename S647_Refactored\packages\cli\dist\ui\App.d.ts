/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import type { CliArgs } from '../config/args.js';
import type { Configuration } from '../config/types.js';
import type { Logger } from '@inkbytefo/s647-shared';
/**
 * Screen types
 */
export type Screen = 'chat' | 'config' | 'providers' | 'tools';
/**
 * App props
 */
export interface AppProps {
    args: CliArgs;
    config: Configuration;
    logger: Logger;
}
/**
 * Main App component
 */
export declare function App({ args, config, logger }: AppProps): React.JSX.Element;
//# sourceMappingURL=App.d.ts.map