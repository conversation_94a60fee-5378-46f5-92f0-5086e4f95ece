{"version": 3, "file": "ProvidersScreen.js", "sourceRoot": "", "sources": ["../../../src/ui/screens/ProvidersScreen.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAChE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAIhC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,gCAAgC,CAAC;AAC3E,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAyBpF,MAAM,UAAU,eAAe,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAwB;IAC7F,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAuB;QACvD,gBAAgB,EAAE,CAAC;QACnB,gBAAgB,EAAE,EAAE;QACpB,OAAO,EAAE,KAAK;KACf,CAAC,CAAC;IAEH,+BAA+B;IAC/B,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,QAAQ,GAAqB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;YAC7F,IAAI;YACJ,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,KAAK;YAClC,SAAS,EAAE,KAAK,EAAE,iBAAiB;YACnC,MAAM,EAAE,EAAE,EAAE,kBAAkB;SAC/B,CAAC,CAAC,CAAC;QAEJ,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IAEvB,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,EAAE,YAAoB,EAAE,EAAE;QAC9D,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;QAC9E,MAAM,CAAC,IAAI,CAAC,yBAAyB,YAAY,KAAK,CAAC,CAAC;QAExD,IAAI,CAAC;YACH,0CAA0C;YAC1C,wDAAwD;YACxD,kDAAkD;YAElD,mBAAmB;YACnB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAExD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChB,GAAG,IAAI;gBACP,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACnD,MAAM,CAAC,IAAI,KAAK,YAAY;oBAC1B,CAAC,CAAC,EAAE,GAAG,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,IAAI,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE;oBAC1E,CAAC,CAAC,MAAM,CACX;gBACD,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC,CAAC;YAEJ,MAAM,CAAC,IAAI,CAAC,KAAK,YAAY,wBAAwB,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChB,GAAG,IAAI;gBACP,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACnD,MAAM,CAAC,IAAI,KAAK,YAAY;oBAC1B,CAAC,CAAC;wBACE,GAAG,MAAM;wBACT,SAAS,EAAE,KAAK;wBAChB,UAAU,EAAE,IAAI,IAAI,EAAE;wBACtB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;qBAChE;oBACH,CAAC,CAAC,MAAM,CACX;gBACD,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,SAAS;aAC3B,CAAC,CAAC,CAAC;YAEJ,MAAM,CAAC,KAAK,CAAC,KAAK,YAAY,uBAAuB,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAExB,MAAM,gBAAgB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC9C,MAAM,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAEvE,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;YACxC,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC;IAE3C,MAAM,oBAAoB,GAAG,WAAW,CAAC,CAAC,KAAa,EAAE,EAAE;QACzD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC3D,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,aAAa,GAAG;QACpB,GAAG,EAAE,GAAG,EAAE;YACR,MAAM,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACxE,IAAI,gBAAgB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACvC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QACD,GAAG,EAAE,GAAG,EAAE;YACR,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACnB,gBAAgB,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;QACD,GAAG,EAAE,GAAG,EAAE;YACR,wBAAwB;YACxB,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAClD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChB,GAAG,IAAI;gBACP,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACrD,GAAG,MAAM;oBACT,SAAS,EAAE,KAAK;oBAChB,UAAU,EAAE,SAAS;oBACrB,KAAK,EAAE,SAAS;iBACjB,CAAC,CAAC;aACJ,CAAC,CAAC,CAAC;QACN,CAAC;QACD,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC;QAC9B,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC;QAChC,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;QACnC,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;KAChC,CAAC;IAEF,MAAM,qBAAqB,GAAG,CAAC,MAAsB,EAAU,EAAE;QAC/D,IAAI,CAAC,MAAM,CAAC,OAAO;YAAE,OAAO,GAAG,CAAC;QAChC,IAAI,KAAK,CAAC,eAAe,KAAK,MAAM,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QACvD,IAAI,MAAM,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC;QAClC,IAAI,MAAM,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,MAAM,qBAAqB,GAAG,CAAC,MAAsB,EAAU,EAAE;QAC/D,IAAI,CAAC,MAAM,CAAC,OAAO;YAAE,OAAO,UAAU,CAAC;QACvC,IAAI,KAAK,CAAC,eAAe,KAAK,MAAM,CAAC,IAAI;YAAE,OAAO,YAAY,CAAC;QAC/D,IAAI,MAAM,CAAC,SAAS;YAAE,OAAO,WAAW,CAAC;QACzC,IAAI,MAAM,CAAC,KAAK;YAAE,OAAO,OAAO,CAAC;QACjC,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aAEpC,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,EAAC,IAAI,2DAEjB,GACH,EAGN,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,6FAEX,GACH,EAGN,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,YACxB,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;oBAC9C,MAAM,UAAU,GAAG,KAAK,KAAK,KAAK,CAAC,gBAAgB,CAAC;oBACpD,MAAM,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAEvD,OAAO,CACL,KAAC,GAAG,IAAqB,YAAY,EAAE,CAAC,YACtC,KAAC,aAAa,IACZ,KAAK,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAC1E,WAAW,EAAE,KAAK,YAElB,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aAEzB,KAAC,UAAU,IACT,KAAK,EAAC,QAAQ,EACd,KAAK,EAAE,qBAAqB,CAAC,QAAQ,CAAC,EACtC,WAAW,EAAE,QAAQ,CAAC,KAAK,IAAI,4BAA4B,GAC3D,EAGF,KAAC,UAAU,IACT,KAAK,EAAC,MAAM,EACZ,KAAK,EAAE,cAAc,EAAE,IAAI,IAAI,SAAS,EACxC,WAAW,EAAC,eAAe,GAC3B,EAEF,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,QAAQ,CAAC,OAAO,EACvB,WAAW,EAAC,kCAAkC,GAC9C,EAEF,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,GAAG,cAAc,EAAE,OAAO,IAAI,KAAK,IAAI,EAC9C,WAAW,EAAC,iBAAiB,GAC7B,EAEF,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,cAAc,EAAE,OAAO,IAAI,CAAC,EACnC,WAAW,EAAC,0BAA0B,GACtC,EAGD,QAAQ,CAAC,UAAU,IAAI,CACtB,KAAC,UAAU,IACT,KAAK,EAAC,aAAa,EACnB,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,cAAc,EAAE,EAC3C,WAAW,EAAC,oCAAoC,GAChD,CACH,EAGA,QAAQ,CAAC,KAAK,IAAI,CACjB,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,MAAC,IAAI,IAAC,KAAK,EAAC,KAAK,wBACP,QAAQ,CAAC,KAAK,IACjB,GACH,CACP,EAGA,UAAU,IAAI,CACb,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,oDAEpB,GACH,CACP,IACG,GACQ,IAjER,QAAQ,CAAC,IAAI,CAkEjB,CACP,CAAC;gBACJ,CAAC,CAAC,GACE,EAGN,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,SAAS,QAAC,OAAO,EAAE,CAAC,YAC1D,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,0BACN,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,gBAAY,GAAG,EAC5E,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,kBAAc,GAAG,EACvE,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,eAC9C,GACH,EAGN,KAAC,aAAa,IAAC,OAAO,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,OAAO,GAAI,EAClE,KAAC,iBAAiB,IAChB,KAAK,EAAE,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAC9C,aAAa,EAAE,KAAK,CAAC,gBAAgB,EACrC,iBAAiB,EAAE,oBAAoB,EACvC,QAAQ,EAAE,KAAK,CAAC,OAAO,GACvB,IACE,CACP,CAAC;AACJ,CAAC"}