/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useCallback } from 'react';
import { Box, Text } from 'ink';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { Screen } from '../App.js';
import { ConfigSection, ConfigItem, ConfigList } from '../components/ConfigSection.js';
import { HotkeyHandler, NavigationHandler } from '../components/KeyboardHandler.js';

interface ConfigScreenProps {
  config: Configuration;
  logger: Logger;
  onNavigate: (screen: Screen) => void;
}

interface ConfigScreenState {
  selectedSection: number;
  collapsedSections: Record<string, boolean>;
  editMode: boolean;
  editingItem?: string;
}

export function ConfigScreen({ config, logger, onNavigate }: ConfigScreenProps): React.JSX.Element {
  const [state, setState] = useState<ConfigScreenState>({
    selectedSection: 0,
    collapsedSections: {},
    editMode: false,
  });

  // Configuration sections
  const sections = [
    'general',
    'providers',
    'tools',
    'ui',
    'logging',
    'telemetry',
    'security',
    'performance'
  ];

  const handleSectionChange = useCallback((index: number) => {
    setState(prev => ({ ...prev, selectedSection: index }));
  }, []);

  const toggleSection = useCallback((sectionName: string) => {
    setState(prev => ({
      ...prev,
      collapsedSections: {
        ...prev.collapsedSections,
        [sectionName]: !prev.collapsedSections[sectionName]
      }
    }));
  }, []);

  const handleHotkeys = {
    'r': () => {
      logger.info('Reloading configuration...');
      // TODO: Implement config reload
    },
    's': () => {
      logger.info('Saving configuration...');
      // TODO: Implement config save
    },
    'e': () => {
      setState(prev => ({ ...prev, editMode: !prev.editMode }));
    },
    'f5': () => onNavigate('chat'),
    'f6': () => onNavigate('config'),
    'f7': () => onNavigate('providers'),
    'f8': () => onNavigate('tools'),
  };

  return (
    <Box flexDirection="column" padding={1}>
      {/* Header */}
      <Box marginBottom={1}>
        <Text color="yellow" bold>
          ⚙️ Configuration Management
        </Text>
      </Box>

      {/* Help text */}
      <Box marginBottom={1}>
        <Text color="gray">
          Navigate: ↑↓ | Toggle: Enter | Edit: E | Reload: R | Save: S | Screens: F5-F8
        </Text>
      </Box>

      {/* Configuration sections */}
      <Box flexDirection="column">
        {/* General Configuration */}
        <ConfigSection
          title="General Configuration"
          collapsible
          collapsed={state.collapsedSections.general ?? false}
          onToggle={() => toggleSection('general')}
        >
          <ConfigItem
            label="Version"
            value={config.version}
            description="Current configuration version"
          />
          <ConfigItem
            label="Environment"
            value={config.environment}
            description="Runtime environment (development, production, test)"
            editable={state.editMode}
          />
          <ConfigItem
            label="Default Provider"
            value={config.defaultProvider}
            description="Default AI provider to use"
            editable={state.editMode}
          />
        </ConfigSection>

        {/* Providers Configuration */}
        <ConfigSection
          title="AI Providers"
          collapsible
          collapsed={state.collapsedSections.providers ?? false}
          onToggle={() => toggleSection('providers')}
        >
          {Object.entries(config.providers).map(([name, provider]) => (
            <Box key={name} flexDirection="column" marginBottom={1}>
              <Text color="cyan">{name.toUpperCase()}</Text>
              <Box marginLeft={2} flexDirection="column">
                <ConfigItem
                  label="Type"
                  value={provider.type}
                  description={`Provider type: ${provider.type}`}
                />
                <ConfigItem
                  label="Enabled"
                  value={provider.enabled ?? false}
                  description="Whether this provider is enabled"
                  editable={state.editMode}
                />
                <ConfigItem
                  label="Timeout"
                  value={`${provider.timeout ?? 30000}ms`}
                  description="Request timeout in milliseconds"
                  editable={state.editMode}
                />
                <ConfigItem
                  label="Retries"
                  value={provider.retries ?? 3}
                  description="Number of retry attempts"
                  editable={state.editMode}
                />
              </Box>
            </Box>
          ))}
        </ConfigSection>

        {/* Tools Configuration */}
        <ConfigSection
          title="Tools Configuration"
          collapsible
          collapsed={state.collapsedSections.tools ?? false}
          onToggle={() => toggleSection('tools')}
        >
          <ConfigItem
            label="Enabled Tools"
            value={config.tools.enabled.join(', ')}
            description="Currently enabled tools"
            editable={state.editMode}
          />
          {Object.entries(config.tools).filter(([key]) => key !== 'enabled').map(([name, toolConfig]) => (
            <Box key={name} flexDirection="column" marginBottom={1}>
              <Text color="cyan">{name.toUpperCase()}</Text>
              <Box marginLeft={2} flexDirection="column">
                {typeof toolConfig === 'object' && toolConfig && (
                  <>
                    <ConfigItem
                      label="Enabled"
                      value={(toolConfig as any).enabled ?? true}
                      description={`Whether ${name} tool is enabled`}
                      editable={state.editMode}
                    />
                    <ConfigItem
                      label="Timeout"
                      value={`${(toolConfig as any).timeout ?? 'N/A'}ms`}
                      description="Tool timeout in milliseconds"
                      editable={state.editMode}
                    />
                  </>
                )}
              </Box>
            </Box>
          ))}
        </ConfigSection>

        {/* UI Configuration */}
        <ConfigSection
          title="User Interface"
          collapsible
          collapsed={state.collapsedSections.ui ?? false}
          onToggle={() => toggleSection('ui')}
        >
          <ConfigItem
            label="Theme"
            value={config.ui.theme}
            description="UI color theme"
            editable={state.editMode}
          />
          <ConfigItem
            label="Animations"
            value={config.ui.animations}
            description="Enable UI animations"
            editable={state.editMode}
          />
          <ConfigItem
            label="Verbose Mode"
            value={config.ui.verbose}
            description="Show detailed information"
            editable={state.editMode}
          />
          <ConfigItem
            label="Show Timestamps"
            value={config.ui.showTimestamps}
            description="Display timestamps in messages"
            editable={state.editMode}
          />
          <ConfigItem
            label="Auto Scroll"
            value={config.ui.autoScroll}
            description="Automatically scroll to new content"
            editable={state.editMode}
          />
        </ConfigSection>

        {/* Logging Configuration */}
        <ConfigSection
          title="Logging"
          collapsible
          collapsed={state.collapsedSections.logging ?? false}
          onToggle={() => toggleSection('logging')}
        >
          <ConfigItem
            label="Log Level"
            value={config.logging.level}
            description="Minimum log level to display"
            editable={state.editMode}
          />
          <ConfigItem
            label="Log Format"
            value={config.logging.format}
            description="Log output format"
            editable={state.editMode}
          />
          <ConfigItem
            label="Log Output"
            value={config.logging.output}
            description="Where to output logs"
            editable={state.editMode}
          />
          <ConfigItem
            label="Include Timestamp"
            value={config.logging.includeTimestamp}
            description="Include timestamps in log entries"
            editable={state.editMode}
          />
        </ConfigSection>

        {/* Telemetry Configuration */}
        <ConfigSection
          title="Telemetry & Analytics"
          collapsible
          collapsed={state.collapsedSections.telemetry ?? false}
          onToggle={() => toggleSection('telemetry')}
        >
          <ConfigItem
            label="Telemetry Enabled"
            value={config.telemetry.enabled}
            description="Enable anonymous usage analytics"
            editable={state.editMode}
          />
          <ConfigItem
            label="Collect Usage"
            value={config.telemetry.collectUsage}
            description="Collect usage statistics"
            editable={state.editMode}
          />
          <ConfigItem
            label="Collect Errors"
            value={config.telemetry.collectErrors}
            description="Collect error reports"
            editable={state.editMode}
          />
          <ConfigItem
            label="Anonymize Data"
            value={config.telemetry.anonymize}
            description="Anonymize collected data"
            editable={state.editMode}
          />
        </ConfigSection>
      </Box>

      {/* Keyboard handlers */}
      <HotkeyHandler hotkeys={handleHotkeys} />
      <NavigationHandler
        items={sections}
        selectedIndex={state.selectedSection}
        onSelectionChange={handleSectionChange}
      />
    </Box>
  );
}
