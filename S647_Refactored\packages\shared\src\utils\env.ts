/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import { config } from 'dotenv';
import { z } from 'zod';
import path from 'path';
import fs from 'fs';

/**
 * Environment configuration schema
 */
const EnvSchema = z.object({
  // AI Providers
  OPENAI_API_KEY: z.string().optional(),
  OPENAI_BASE_URL: z.string().default('https://api.openai.com/v1'),
  OPENAI_MODEL: z.string().default('gpt-4'),
  OPENAI_MAX_TOKENS: z.coerce.number().default(4096),
  OPENAI_TEMPERATURE: z.coerce.number().default(0.7),

  ANTHROPIC_API_KEY: z.string().optional(),
  ANTHROPIC_BASE_URL: z.string().default('https://api.anthropic.com'),
  ANTHROPIC_MODEL: z.string().default('claude-3-sonnet-20240229'),
  ANTHROPIC_MAX_TOKENS: z.coerce.number().default(4096),
  ANTHROPIC_TEMPERATURE: z.coerce.number().default(0.7),

  GOOGLE_API_KEY: z.string().optional(),
  GOOGLE_BASE_URL: z.string().default('https://generativelanguage.googleapis.com'),
  GOOGLE_MODEL: z.string().default('gemini-pro'),
  GOOGLE_MAX_TOKENS: z.coerce.number().default(4096),
  GOOGLE_TEMPERATURE: z.coerce.number().default(0.7),

  MISTRAL_API_KEY: z.string().optional(),
  MISTRAL_BASE_URL: z.string().default('https://api.mistral.ai'),
  MISTRAL_MODEL: z.string().default('mistral-large-latest'),
  MISTRAL_MAX_TOKENS: z.coerce.number().default(4096),
  MISTRAL_TEMPERATURE: z.coerce.number().default(0.7),

  // Default provider
  DEFAULT_PROVIDER: z.enum(['openai', 'anthropic', 'google', 'mistral']).default('openai'),

  // Application settings
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  LOG_FORMAT: z.enum(['text', 'json']).default('text'),
  DEBUG: z.coerce.boolean().default(false),

  // UI Settings
  UI_THEME: z.enum(['dark', 'light']).default('dark'),
  UI_ANIMATIONS: z.coerce.boolean().default(true),
  UI_SHOW_TIMESTAMPS: z.coerce.boolean().default(true),
  UI_SHOW_TOKEN_COUNT: z.coerce.boolean().default(true),
  UI_MAX_HISTORY_LINES: z.coerce.number().default(1000),

  // Performance
  MAX_CONCURRENT_REQUESTS: z.coerce.number().default(10),
  REQUEST_TIMEOUT: z.coerce.number().default(30000),
  RETRY_ATTEMPTS: z.coerce.number().default(3),
  RETRY_DELAY: z.coerce.number().default(1000),

  // Security
  SANDBOX_ENABLED: z.coerce.boolean().default(false),
  ENCRYPTION_ENABLED: z.coerce.boolean().default(false),
  TELEMETRY_ENABLED: z.coerce.boolean().default(false),

  // MCP Settings
  MCP_ENABLED: z.coerce.boolean().default(true),
  MCP_SERVERS_CONFIG_PATH: z.string().default('./mcp-servers.json'),

  // Memory Management
  MEMORY_ENABLED: z.coerce.boolean().default(true),
  MEMORY_MAX_SIZE: z.coerce.number().default(100),
  MEMORY_TTL: z.coerce.number().default(3600),

  // File Integration
  FILE_INTEGRATION_ENABLED: z.coerce.boolean().default(true),
  FILE_MAX_SIZE: z.coerce.number().default(10485760), // 10MB
  FILE_ALLOWED_EXTENSIONS: z.string().default('.txt,.md,.js,.ts,.tsx,.jsx,.py,.java,.cpp,.c,.h,.css,.html,.json,.yaml,.yml,.xml,.sql,.sh,.bat,.ps1'),

  // Development
  NODE_ENV: z.enum(['development', 'production', 'test']).default('production'),
  PORT: z.coerce.number().default(3000),
});

export type EnvConfig = z.infer<typeof EnvSchema>;

/**
 * Load and validate environment configuration
 */
export function loadEnvConfig(envPath?: string): EnvConfig {
  // Load .env file
  const rootDir = findProjectRoot();
  const defaultEnvPath = path.join(rootDir, '.env');
  const envFilePath = envPath || defaultEnvPath;

  if (fs.existsSync(envFilePath)) {
    config({ path: envFilePath });
  }

  // Parse and validate environment variables
  const result = EnvSchema.safeParse(process.env);

  if (!result.success) {
    const errors = result.error.errors.map(err => 
      `${err.path.join('.')}: ${err.message}`
    ).join('\n');
    
    throw new Error(`Environment configuration validation failed:\n${errors}`);
  }

  return result.data;
}

/**
 * Find project root directory
 */
function findProjectRoot(): string {
  let currentDir = process.cwd();
  
  while (currentDir !== path.dirname(currentDir)) {
    if (fs.existsSync(path.join(currentDir, 'package.json'))) {
      return currentDir;
    }
    currentDir = path.dirname(currentDir);
  }
  
  return process.cwd();
}

/**
 * Get provider configuration from environment
 */
export function getProviderConfig(env: EnvConfig, provider: string) {
  switch (provider) {
    case 'openai':
      return {
        type: 'openai' as const,
        apiKey: env.OPENAI_API_KEY,
        baseUrl: env.OPENAI_BASE_URL,
        model: env.OPENAI_MODEL,
        maxTokens: env.OPENAI_MAX_TOKENS,
        temperature: env.OPENAI_TEMPERATURE,
        enabled: !!env.OPENAI_API_KEY,
      };
    
    case 'anthropic':
      return {
        type: 'anthropic' as const,
        apiKey: env.ANTHROPIC_API_KEY,
        baseUrl: env.ANTHROPIC_BASE_URL,
        model: env.ANTHROPIC_MODEL,
        maxTokens: env.ANTHROPIC_MAX_TOKENS,
        temperature: env.ANTHROPIC_TEMPERATURE,
        enabled: !!env.ANTHROPIC_API_KEY,
      };
    
    case 'google':
      return {
        type: 'google' as const,
        apiKey: env.GOOGLE_API_KEY,
        baseUrl: env.GOOGLE_BASE_URL,
        model: env.GOOGLE_MODEL,
        maxTokens: env.GOOGLE_MAX_TOKENS,
        temperature: env.GOOGLE_TEMPERATURE,
        enabled: !!env.GOOGLE_API_KEY,
      };
    
    case 'mistral':
      return {
        type: 'mistral' as const,
        apiKey: env.MISTRAL_API_KEY,
        baseUrl: env.MISTRAL_BASE_URL,
        model: env.MISTRAL_MODEL,
        maxTokens: env.MISTRAL_MAX_TOKENS,
        temperature: env.MISTRAL_TEMPERATURE,
        enabled: !!env.MISTRAL_API_KEY,
      };
    
    default:
      throw new Error(`Unknown provider: ${provider}`);
  }
}

/**
 * Get all available providers from environment
 */
export function getAvailableProviders(env: EnvConfig) {
  const providers = ['openai', 'anthropic', 'google', 'mistral'];
  return providers
    .map(provider => getProviderConfig(env, provider))
    .filter(config => config.enabled);
}

/**
 * Validate that at least one provider is configured
 */
export function validateProviders(env: EnvConfig): void {
  const availableProviders = getAvailableProviders(env);
  
  if (availableProviders.length === 0) {
    throw new Error(
      'No AI providers configured. Please add at least one API key to your .env file:\n' +
      '- OPENAI_API_KEY\n' +
      '- ANTHROPIC_API_KEY\n' +
      '- GOOGLE_API_KEY\n' +
      '- MISTRAL_API_KEY'
    );
  }

  // Validate default provider is available
  const defaultProvider = getProviderConfig(env, env.DEFAULT_PROVIDER);
  if (!defaultProvider.enabled) {
    const firstAvailable = availableProviders[0];
    console.warn(
      `Default provider '${env.DEFAULT_PROVIDER}' is not configured. ` +
      `Using '${firstAvailable.type}' instead.`
    );
  }
}
