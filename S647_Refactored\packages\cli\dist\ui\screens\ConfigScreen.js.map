{"version": 3, "file": "ConfigScreen.js", "sourceRoot": "", "sources": ["../../../src/ui/screens/ConfigScreen.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACrD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC;AAGhC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,gCAAgC,CAAC;AACvF,OAAO,EAAE,aAAa,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAepF,MAAM,UAAU,YAAY,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAqB;IAC5E,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAoB;QACpD,eAAe,EAAE,CAAC;QAClB,iBAAiB,EAAE,EAAE;QACrB,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,yBAAyB;IACzB,MAAM,QAAQ,GAAG;QACf,SAAS;QACT,WAAW;QACX,OAAO;QACP,IAAI;QACJ,SAAS;QACT,WAAW;QACX,UAAU;QACV,aAAa;KACd,CAAC;IAEF,MAAM,mBAAmB,GAAG,WAAW,CAAC,CAAC,KAAa,EAAE,EAAE;QACxD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC1D,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,WAAmB,EAAE,EAAE;QACxD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChB,GAAG,IAAI;YACP,iBAAiB,EAAE;gBACjB,GAAG,IAAI,CAAC,iBAAiB;gBACzB,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;aACpD;SACF,CAAC,CAAC,CAAC;IACN,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,aAAa,GAAG;QACpB,GAAG,EAAE,GAAG,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC1C,gCAAgC;QAClC,CAAC;QACD,GAAG,EAAE,GAAG,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACvC,8BAA8B;QAChC,CAAC;QACD,GAAG,EAAE,GAAG,EAAE;YACR,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC;QAC9B,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC;QAChC,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;QACnC,IAAI,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;KAChC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aAEpC,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,4DAElB,GACH,EAGN,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,wGAEX,GACH,EAGN,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aAEzB,MAAC,aAAa,IACZ,KAAK,EAAC,uBAAuB,EAC7B,WAAW,QACX,SAAS,EAAE,KAAK,CAAC,iBAAiB,CAAC,OAAO,IAAI,KAAK,EACnD,QAAQ,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC,aAExC,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,MAAM,CAAC,OAAO,EACrB,WAAW,EAAC,+BAA+B,GAC3C,EACF,KAAC,UAAU,IACT,KAAK,EAAC,aAAa,EACnB,KAAK,EAAE,MAAM,CAAC,WAAW,EACzB,WAAW,EAAC,qDAAqD,EACjE,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,kBAAkB,EACxB,KAAK,EAAE,MAAM,CAAC,eAAe,EAC7B,WAAW,EAAC,4BAA4B,EACxC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACY,EAGhB,KAAC,aAAa,IACZ,KAAK,EAAC,cAAc,EACpB,WAAW,QACX,SAAS,EAAE,KAAK,CAAC,iBAAiB,CAAC,SAAS,IAAI,KAAK,EACrD,QAAQ,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC,YAEzC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC,CAC1D,MAAC,GAAG,IAAY,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACpD,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,YAAE,IAAI,CAAC,WAAW,EAAE,GAAQ,EAC9C,MAAC,GAAG,IAAC,UAAU,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aACxC,KAAC,UAAU,IACT,KAAK,EAAC,MAAM,EACZ,KAAK,EAAE,QAAQ,CAAC,IAAI,EACpB,WAAW,EAAE,kBAAkB,QAAQ,CAAC,IAAI,EAAE,GAC9C,EACF,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,QAAQ,CAAC,OAAO,IAAI,KAAK,EAChC,WAAW,EAAC,kCAAkC,EAC9C,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,GAAG,QAAQ,CAAC,OAAO,IAAI,KAAK,IAAI,EACvC,WAAW,EAAC,iCAAiC,EAC7C,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,QAAQ,CAAC,OAAO,IAAI,CAAC,EAC5B,WAAW,EAAC,0BAA0B,EACtC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACE,KA1BE,IAAI,CA2BR,CACP,CAAC,GACY,EAGhB,MAAC,aAAa,IACZ,KAAK,EAAC,qBAAqB,EAC3B,WAAW,QACX,SAAS,EAAE,KAAK,CAAC,iBAAiB,CAAC,KAAK,IAAI,KAAK,EACjD,QAAQ,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,aAEtC,KAAC,UAAU,IACT,KAAK,EAAC,eAAe,EACrB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EACtC,WAAW,EAAC,yBAAyB,EACrC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC,CAC7F,MAAC,GAAG,IAAY,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACpD,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,YAAE,IAAI,CAAC,WAAW,EAAE,GAAQ,EAC9C,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,YACvC,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,IAAI,CAC/C,8BACE,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAG,UAAkB,CAAC,OAAO,IAAI,IAAI,EAC1C,WAAW,EAAE,WAAW,IAAI,kBAAkB,EAC9C,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,SAAS,EACf,KAAK,EAAE,GAAI,UAAkB,CAAC,OAAO,IAAI,KAAK,IAAI,EAClD,WAAW,EAAC,8BAA8B,EAC1C,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACD,CACJ,GACG,KAnBE,IAAI,CAoBR,CACP,CAAC,IACY,EAGhB,MAAC,aAAa,IACZ,KAAK,EAAC,gBAAgB,EACtB,WAAW,QACX,SAAS,EAAE,KAAK,CAAC,iBAAiB,CAAC,EAAE,IAAI,KAAK,EAC9C,QAAQ,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,aAEnC,KAAC,UAAU,IACT,KAAK,EAAC,OAAO,EACb,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EACtB,WAAW,EAAC,gBAAgB,EAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,YAAY,EAClB,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAC3B,WAAW,EAAC,sBAAsB,EAClC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,cAAc,EACpB,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EACxB,WAAW,EAAC,2BAA2B,EACvC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,iBAAiB,EACvB,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,cAAc,EAC/B,WAAW,EAAC,gCAAgC,EAC5C,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,aAAa,EACnB,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,UAAU,EAC3B,WAAW,EAAC,qCAAqC,EACjD,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACY,EAGhB,MAAC,aAAa,IACZ,KAAK,EAAC,SAAS,EACf,WAAW,QACX,SAAS,EAAE,KAAK,CAAC,iBAAiB,CAAC,OAAO,IAAI,KAAK,EACnD,QAAQ,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,SAAS,CAAC,aAExC,KAAC,UAAU,IACT,KAAK,EAAC,WAAW,EACjB,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EAC3B,WAAW,EAAC,8BAA8B,EAC1C,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,YAAY,EAClB,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,EAC5B,WAAW,EAAC,mBAAmB,EAC/B,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,YAAY,EAClB,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,EAC5B,WAAW,EAAC,sBAAsB,EAClC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,mBAAmB,EACzB,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,gBAAgB,EACtC,WAAW,EAAC,mCAAmC,EAC/C,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACY,EAGhB,MAAC,aAAa,IACZ,KAAK,EAAC,uBAAuB,EAC7B,WAAW,QACX,SAAS,EAAE,KAAK,CAAC,iBAAiB,CAAC,SAAS,IAAI,KAAK,EACrD,QAAQ,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,WAAW,CAAC,aAE1C,KAAC,UAAU,IACT,KAAK,EAAC,mBAAmB,EACzB,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO,EAC/B,WAAW,EAAC,kCAAkC,EAC9C,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,eAAe,EACrB,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,YAAY,EACpC,WAAW,EAAC,0BAA0B,EACtC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,gBAAgB,EACtB,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,aAAa,EACrC,WAAW,EAAC,uBAAuB,EACnC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,EACF,KAAC,UAAU,IACT,KAAK,EAAC,gBAAgB,EACtB,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,SAAS,EACjC,WAAW,EAAC,0BAA0B,EACtC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GACxB,IACY,IACZ,EAGN,KAAC,aAAa,IAAC,OAAO,EAAE,aAAa,GAAI,EACzC,KAAC,iBAAiB,IAChB,KAAK,EAAE,QAAQ,EACf,aAAa,EAAE,KAAK,CAAC,eAAe,EACpC,iBAAiB,EAAE,mBAAmB,GACtC,IACE,CACP,CAAC;AACJ,CAAC"}