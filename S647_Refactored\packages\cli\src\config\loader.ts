/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import type { Configuration, AsyncResult } from '@inkbytefo/s647-shared';
import { ConfigurationManager } from '@inkbytefo/s647-core-refactored';
import type { CliArgs } from './args.js';

/**
 * Load configuration using the configuration manager
 */
export async function loadConfiguration(args: CliArgs): Promise<Configuration> {
  const configManager = new ConfigurationManager();

  // Prepare loader options from CLI args
  const loaderOptions = {
    baseDir: process.cwd(),
    envPrefix: 'S647_',
    args: args as Record<string, any>,
  };

  // Load configuration from all sources
  const result = await configManager.load(loaderOptions);

  if (!result.success) {
    // Log warnings if any
    if (result.warnings) {
      console.warn('Configuration warnings:', result.warnings.join(', '));
    }

    // Throw error if loading failed
    throw result.error || new Error('Failed to load configuration');
  }

  // Log warnings if any
  if (result.warnings) {
    console.warn('Configuration warnings:', result.warnings.join(', '));
  }

  return result.data;
}
