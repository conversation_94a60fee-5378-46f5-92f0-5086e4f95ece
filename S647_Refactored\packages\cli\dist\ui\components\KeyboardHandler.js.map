{"version": 3, "file": "KeyboardHandler.js", "sourceRoot": "", "sources": ["../../../src/ui/components/KeyboardHandler.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAuB/B;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,EAC9B,UAAU,EACV,SAAS,EACT,WAAW,EACX,WAAW,EACX,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,KAAK,EACL,WAAW,EACX,QAAQ,EACR,MAAM,EACN,KAAK,EACL,QAAQ,EACR,UAAU,EACV,QAAQ,GAAG,KAAK,EACK;IACrB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,QAAQ;YAAE,OAAO;QAErB,iCAAiC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACzB,CAAC;QAED,uBAAuB;QACvB,IAAI,GAAG,CAAC,OAAO,IAAI,SAAS,EAAE,CAAC;YAC7B,SAAS,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,GAAG,CAAC,SAAS,IAAI,WAAW,EAAE,CAAC;YACxC,WAAW,EAAE,CAAC;QAChB,CAAC;aAAM,IAAI,GAAG,CAAC,SAAS,IAAI,WAAW,EAAE,CAAC;YACxC,WAAW,EAAE,CAAC;QAChB,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,IAAI,YAAY,EAAE,CAAC;YAC1C,YAAY,EAAE,CAAC;QACjB,CAAC;aAAM,IAAI,GAAG,CAAC,MAAM,IAAI,OAAO,EAAE,CAAC;YACjC,OAAO,EAAE,CAAC;QACZ,CAAC;aAAM,IAAI,GAAG,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;YAClC,QAAQ,EAAE,CAAC;QACb,CAAC;aAAM,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC;YAC5B,KAAK,EAAE,CAAC;QACV,CAAC;aAAM,IAAI,GAAG,CAAC,SAAS,IAAI,WAAW,EAAE,CAAC;YACxC,WAAW,EAAE,CAAC;QAChB,CAAC;aAAM,IAAI,GAAG,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;YAClC,QAAQ,EAAE,CAAC;QACb,CAAC;aAAM,IAAK,GAAW,CAAC,IAAI,IAAI,MAAM,EAAE,CAAC;YACvC,MAAM,EAAE,CAAC;QACX,CAAC;aAAM,IAAK,GAAW,CAAC,GAAG,IAAI,KAAK,EAAE,CAAC;YACrC,KAAK,EAAE,CAAC;QACV,CAAC;aAAM,IAAI,GAAG,CAAC,MAAM,IAAI,QAAQ,EAAE,CAAC;YAClC,QAAQ,EAAE,CAAC;QACb,CAAC;aAAM,IAAI,GAAG,CAAC,QAAQ,IAAI,UAAU,EAAE,CAAC;YACtC,UAAU,EAAE,CAAC;QACf,CAAC;IACH,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;IAE5B,OAAO,IAAI,CAAC,CAAC,yCAAyC;AACxD,CAAC;AAcD;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,EAChC,KAAK,EACL,aAAa,EACb,iBAAiB,EACjB,QAAQ,EACR,QAAQ,GAAG,KAAK,EAChB,IAAI,GAAG,IAAI,EACY;IACvB,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,iBAAiB,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACtC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,aAAa,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,iBAAiB,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,IAAI,QAAQ,IAAI,aAAa,IAAI,CAAC,IAAI,aAAa,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YACnE,MAAM,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;YAClC,IAAI,IAAI,EAAE,CAAC;gBACT,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,KAAC,eAAe,IACd,SAAS,EAAE,aAAa,EACxB,WAAW,EAAE,eAAe,EAC5B,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,QAAQ,GAClB,CACH,CAAC;AACJ,CAAC;AAUD;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,EAC5B,OAAO,EACP,QAAQ,GAAG,KAAK,EACG;IACnB,MAAM,cAAc,GAAG,CAAC,KAAa,EAAE,GAAQ,EAAE,EAAE;QACjD,uBAAuB;QACvB,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;YAAE,OAAO,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG;YAAE,OAAO,CAAC,GAAG,EAAE,CAAC;QAC1C,IAAI,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG;YAAE,OAAO,CAAC,GAAG,EAAE,CAAC;QAC1C,IAAI,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG;YAAE,OAAO,CAAC,GAAG,EAAE,CAAC;QAE1C,wBAAwB;QACxB,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;YAC7C,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACb,MAAM,OAAO,GAAG,QAAQ,KAAK,EAAE,CAAC;YAChC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,KAAC,eAAe,IACd,UAAU,EAAE,cAAc,EAC1B,QAAQ,EAAE,QAAQ,GAClB,CACH,CAAC;AACJ,CAAC"}