{"version": 3, "file": "App.js", "sourceRoot": "", "sources": ["../../src/ui/App.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAIlD,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAC;AA0B5D;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAY;IACpD,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC;IAC1B,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAW;QAC3C,aAAa,EAAE,MAAM;QACrB,SAAS,EAAE,IAAI;KAChB,CAAC,CAAC;IAEH,wBAAwB;IACxB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;gBACxC,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,UAAU,CACvC,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,eAAe,CACvB,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpB,MAAM,MAAM,CAAC,KAAK,CAAC;gBACrB,CAAC;gBAED,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,SAAS,EAAE,KAAK;iBACjB,CAAC,CAAC,CAAC;gBAEJ,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtE,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;gBAExE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAChB,GAAG,IAAI;oBACP,KAAK,EAAE,GAAG;oBACV,SAAS,EAAE,KAAK;iBACjB,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC,CAAC;QAEF,YAAY,EAAE,CAAC;IACjB,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAErB,2CAA2C;IAC3C,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,yBAAyB;QACzB,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,IAAI,EAAE,CAAC;YACP,OAAO;QACT,CAAC;QAED,oEAAoE;QACpE,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QACzD,CAAC;aAAM,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3D,CAAC;aAAM,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,mCAAmC;QACnC,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAClB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QACzD,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC3D,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACzB,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;IAEnC,qBAAqB;IACrB,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,GAAG,EAAE;YACV,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACpB,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;IAEtB,oBAAoB;IACpB,MAAM,gBAAgB,GAAG,CAAC,MAAc,EAAE,EAAE;QAC1C,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;IAEF,uBAAuB;IACvB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,aACvC,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,YAC3D,KAAC,cAAc,IAAC,IAAI,EAAC,sBAAsB,GAAG,GAC1C,EACN,KAAC,SAAS,IACR,MAAM,EAAC,SAAS,EAChB,OAAO,EAAC,4BAA4B,GACpC,IACE,CACP,CAAC;IACJ,CAAC;IAED,qBAAqB;IACrB,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,aACvC,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,YAC3D,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,aAC7C,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,EAAC,IAAI,uDAEf,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,YACd,KAAK,CAAC,KAAK,CAAC,OAAO,GACf,EACP,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,qCAEX,GACH,IACF,GACF,EACN,KAAC,SAAS,IACR,MAAM,EAAC,OAAO,EACd,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAC5B,IACE,CACP,CAAC;IACJ,CAAC;IAED,kBAAkB;IAClB,OAAO,CACL,KAAC,aAAa,cACZ,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,aACvC,MAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,aACb,KAAK,CAAC,aAAa,KAAK,MAAM,IAAI,CACjC,KAAC,UAAU,IACT,SAAS,EAAE,KAAK,CAAC,SAAU,EAC3B,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,gBAAgB,GAC5B,CACH,EACA,KAAK,CAAC,aAAa,KAAK,QAAQ,IAAI,CACnC,KAAC,YAAY,IACX,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,gBAAgB,GAC5B,CACH,EACA,KAAK,CAAC,aAAa,KAAK,WAAW,IAAI,CACtC,KAAC,eAAe,IACd,SAAS,EAAE,KAAK,CAAC,SAAU,EAC3B,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,gBAAgB,GAC5B,CACH,EACA,KAAK,CAAC,aAAa,KAAK,OAAO,IAAI,CAClC,KAAC,WAAW,IACV,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,gBAAgB,GAC5B,CACH,IACG,EAEN,KAAC,SAAS,IACR,MAAM,EAAC,OAAO,EACd,OAAO,EAAE,WAAW,KAAK,CAAC,aAAa,sFAAsF,GAC7H,IACE,GACQ,CACjB,CAAC;AACJ,CAAC"}