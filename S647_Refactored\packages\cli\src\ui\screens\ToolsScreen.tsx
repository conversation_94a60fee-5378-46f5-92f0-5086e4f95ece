/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Box, Text } from 'ink';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { Screen } from '../App.js';
import { ConfigSection, ConfigItem } from '../components/ConfigSection.js';
import { HotkeyHandler, NavigationHandler } from '../components/KeyboardHandler.js';

interface ToolsScreenProps {
  config: Configuration;
  logger: Logger;
  onNavigate: (screen: Screen) => void;
}

interface ToolStatus {
  name: string;
  enabled: boolean;
  available: boolean;
  description: string;
  category: string;
  lastUsed?: Date | undefined;
  usageCount?: number | undefined;
  error?: string | undefined;
}

interface ToolsScreenState {
  selectedTool: number;
  toolStatuses: ToolStatus[];
  filterCategory: string;
  showOnlyEnabled: boolean;
}

const TOOL_DESCRIPTIONS: Record<string, { description: string; category: string }> = {
  file: { description: 'File system operations (read, write, search)', category: 'File System' },
  git: { description: 'Git repository operations and version control', category: 'Version Control' },
  web: { description: 'Web scraping and HTTP requests', category: 'Network' },
  shell: { description: 'Shell command execution', category: 'System' },
  memory: { description: 'Memory and context management', category: 'AI' },
  search: { description: 'Search and indexing operations', category: 'Data' },
  database: { description: 'Database operations and queries', category: 'Data' },
  api: { description: 'API integration and external services', category: 'Network' },
};

export function ToolsScreen({ config, logger, onNavigate }: ToolsScreenProps): React.JSX.Element {
  const [state, setState] = useState<ToolsScreenState>({
    selectedTool: 0,
    toolStatuses: [],
    filterCategory: 'All',
    showOnlyEnabled: false,
  });

  // Initialize tool statuses
  useEffect(() => {
    const enabledTools = config.tools.enabled || [];
    const allToolNames = [
      ...enabledTools,
      ...Object.keys(TOOL_DESCRIPTIONS).filter(name => !enabledTools.includes(name))
    ];

    const statuses: ToolStatus[] = allToolNames.map(name => {
      const toolInfo = TOOL_DESCRIPTIONS[name] || { description: 'Unknown tool', category: 'Other' };

      return {
        name,
        enabled: enabledTools.includes(name),
        available: true, // TODO: Check actual availability
        description: toolInfo.description,
        category: toolInfo.category,
        usageCount: 0, // TODO: Get from usage stats
      };
    });

    setState(prev => ({ ...prev, toolStatuses: statuses }));
  }, [config.tools]);

  const getFilteredTools = useCallback(() => {
    let filtered = state.toolStatuses;

    if (state.filterCategory !== 'All') {
      filtered = filtered.filter(tool => tool.category === state.filterCategory);
    }

    if (state.showOnlyEnabled) {
      filtered = filtered.filter(tool => tool.enabled);
    }

    return filtered;
  }, [state.toolStatuses, state.filterCategory, state.showOnlyEnabled]);

  const toggleTool = useCallback((toolName: string) => {
    setState(prev => ({
      ...prev,
      toolStatuses: prev.toolStatuses.map(tool =>
        tool.name === toolName
          ? { ...tool, enabled: !tool.enabled }
          : tool
      )
    }));

    const tool = state.toolStatuses.find(t => t.name === toolName);
    if (tool) {
      logger.info(`${tool.enabled ? 'Disabled' : 'Enabled'} tool: ${toolName}`);
    }
  }, [state.toolStatuses, logger]);

  const testTool = useCallback(async (toolName: string) => {
    logger.info(`Testing tool: ${toolName}...`);

    try {
      // TODO: Implement actual tool testing
      // Simulate testing
      await new Promise(resolve => setTimeout(resolve, 500));

      setState(prev => ({
        ...prev,
        toolStatuses: prev.toolStatuses.map(tool =>
          tool.name === toolName
            ? { ...tool, available: true, error: undefined, lastUsed: new Date() }
            : tool
        )
      }));

      logger.info(`✅ Tool ${toolName} test successful`);
    } catch (error) {
      setState(prev => ({
        ...prev,
        toolStatuses: prev.toolStatuses.map(tool =>
          tool.name === toolName
            ? {
                ...tool,
                available: false,
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            : tool
        )
      }));

      logger.error(`❌ Tool ${toolName} test failed: ${error}`);
    }
  }, [logger]);

  const handleToolChange = useCallback((index: number) => {
    setState(prev => ({ ...prev, selectedTool: index }));
  }, []);

  const cycleFilter = useCallback(() => {
    const categories = ['All', ...new Set(state.toolStatuses.map(t => t.category))];
    const currentIndex = categories.indexOf(state.filterCategory);
    const nextIndex = (currentIndex + 1) % categories.length;
    setState(prev => ({ ...prev, filterCategory: categories[nextIndex] || 'All' }));
  }, [state.filterCategory, state.toolStatuses]);

  const handleHotkeys = {
    't': () => {
      const filteredTools = getFilteredTools();
      const selectedTool = filteredTools[state.selectedTool];
      if (selectedTool) {
        testTool(selectedTool.name);
      }
    },
    'e': () => {
      const filteredTools = getFilteredTools();
      const selectedTool = filteredTools[state.selectedTool];
      if (selectedTool) {
        toggleTool(selectedTool.name);
      }
    },
    'f': () => {
      cycleFilter();
    },
    'o': () => {
      setState(prev => ({ ...prev, showOnlyEnabled: !prev.showOnlyEnabled }));
    },
    'f5': () => onNavigate('chat'),
    'f6': () => onNavigate('config'),
    'f7': () => onNavigate('providers'),
    'f8': () => onNavigate('tools'),
  };

  const getToolStatusIcon = (tool: ToolStatus): string => {
    if (!tool.enabled) return '⚫';
    if (!tool.available) return '🔴';
    if (tool.error) return '🟡';
    return '🟢';
  };

  const getToolStatusText = (tool: ToolStatus): string => {
    if (!tool.enabled) return 'Disabled';
    if (!tool.available) return 'Unavailable';
    if (tool.error) return 'Error';
    return 'Ready';
  };

  const filteredTools = getFilteredTools();

  return (
    <Box flexDirection="column" padding={1}>
      {/* Header */}
      <Box marginBottom={1}>
        <Text color="magenta" bold>
          🛠️ Tools Management
        </Text>
      </Box>

      {/* Help text */}
      <Box marginBottom={1}>
        <Text color="gray">
          Navigate: ↑↓ | Toggle: E | Test: T | Filter: F | Show Enabled: O | Screens: F5-F8
        </Text>
      </Box>

      {/* Filter controls */}
      <Box marginBottom={1}>
        <Text color="cyan">
          Filter: {state.filterCategory} | Show Only Enabled: {state.showOnlyEnabled ? 'Yes' : 'No'} |
          Total: {filteredTools.length}/{state.toolStatuses.length}
        </Text>
      </Box>

      {/* Tools list */}
      <Box flexDirection="column">
        {filteredTools.map((tool, index) => {
          const isSelected = index === state.selectedTool;
          const toolConfig = config.tools[tool.name as keyof typeof config.tools];

          return (
            <Box key={tool.name} marginBottom={1}>
              <ConfigSection
                title={`${tool.name.toUpperCase()} ${getToolStatusIcon(tool)} [${tool.category}]`}
                collapsible={false}
              >
                <Box flexDirection="column">
                  {/* Description */}
                  <Box marginBottom={1}>
                    <Text color="gray">
                      {tool.description}
                    </Text>
                  </Box>

                  {/* Status */}
                  <ConfigItem
                    label="Status"
                    value={getToolStatusText(tool)}
                    description={tool.error || 'Tool availability status'}
                  />

                  {/* Configuration */}
                  <ConfigItem
                    label="Enabled"
                    value={tool.enabled}
                    description="Whether this tool is enabled"
                  />

                  <ConfigItem
                    label="Category"
                    value={tool.category}
                    description="Tool category"
                  />

                  {typeof toolConfig === 'object' && toolConfig && (
                    <>
                      <ConfigItem
                        label="Timeout"
                        value={`${(toolConfig as any).timeout || 'N/A'}ms`}
                        description="Tool timeout"
                      />

                      <ConfigItem
                        label="Retries"
                        value={(toolConfig as any).retries || 'N/A'}
                        description="Number of retry attempts"
                      />
                    </>
                  )}

                  {/* Usage stats */}
                  {tool.usageCount !== undefined && (
                    <ConfigItem
                      label="Usage Count"
                      value={tool.usageCount}
                      description="Number of times this tool has been used"
                    />
                  )}

                  {/* Last used */}
                  {tool.lastUsed && (
                    <ConfigItem
                      label="Last Used"
                      value={tool.lastUsed.toLocaleString()}
                      description="When this tool was last used"
                    />
                  )}

                  {/* Error details */}
                  {tool.error && (
                    <Box marginTop={1}>
                      <Text color="red">
                        Error: {tool.error}
                      </Text>
                    </Box>
                  )}

                  {/* Action indicator */}
                  {isSelected && (
                    <Box marginTop={1}>
                      <Text color="blue" dimColor>
                        Press E to {tool.enabled ? 'disable' : 'enable'} | Press T to test
                      </Text>
                    </Box>
                  )}
                </Box>
              </ConfigSection>
            </Box>
          );
        })}
      </Box>

      {/* Summary */}
      <Box marginTop={1} borderStyle="single" borderTop padding={1}>
        <Text color="cyan">
          Summary: {state.toolStatuses.filter(t => t.enabled).length} enabled, {' '}
          {state.toolStatuses.filter(t => t.available).length} available, {' '}
          {state.toolStatuses.filter(t => t.error).length} errors
        </Text>
      </Box>

      {/* Keyboard handlers */}
      <HotkeyHandler hotkeys={handleHotkeys} />
      <NavigationHandler
        items={filteredTools.map(t => t.name)}
        selectedIndex={state.selectedTool}
        onSelectionChange={handleToolChange}
      />
    </Box>
  );
}
