/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */

import React from 'react';
import { useInput } from 'ink';

/**
 * Keyboard handler props
 */
export interface KeyboardHandlerProps {
  onKeyPress?: (input: string, key: any) => void;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onEnter?: () => void;
  onEscape?: () => void;
  onTab?: () => void;
  onBackspace?: () => void;
  onDelete?: () => void;
  onHome?: () => void;
  onEnd?: () => void;
  onPageUp?: () => void;
  onPageDown?: () => void;
  disabled?: boolean;
}

/**
 * Keyboard handler component
 */
export function KeyboardHandler({
  onKeyPress,
  onArrowUp,
  onArrowDown,
  onArrowLeft,
  onArrowRight,
  onEnter,
  onEscape,
  onTab,
  onBackspace,
  onDelete,
  onHome,
  onEnd,
  onPageUp,
  onPageDown,
  disabled = false
}: KeyboardHandlerProps): React.JSX.Element | null {
  useInput((input, key) => {
    if (disabled) return;

    // Call general key press handler
    if (onKeyPress) {
      onKeyPress(input, key);
    }

    // Handle specific keys
    if (key.upArrow && onArrowUp) {
      onArrowUp();
    } else if (key.downArrow && onArrowDown) {
      onArrowDown();
    } else if (key.leftArrow && onArrowLeft) {
      onArrowLeft();
    } else if (key.rightArrow && onArrowRight) {
      onArrowRight();
    } else if (key.return && onEnter) {
      onEnter();
    } else if (key.escape && onEscape) {
      onEscape();
    } else if (key.tab && onTab) {
      onTab();
    } else if (key.backspace && onBackspace) {
      onBackspace();
    } else if (key.delete && onDelete) {
      onDelete();
    } else if ((key as any).home && onHome) {
      onHome();
    } else if ((key as any).end && onEnd) {
      onEnd();
    } else if (key.pageUp && onPageUp) {
      onPageUp();
    } else if (key.pageDown && onPageDown) {
      onPageDown();
    }
  }, { isActive: !disabled });

  return null; // This component doesn't render anything
}

/**
 * Navigation handler props
 */
export interface NavigationHandlerProps {
  items: string[];
  selectedIndex: number;
  onSelectionChange: (index: number) => void;
  onSelect?: (index: number, item: string) => void;
  disabled?: boolean;
  wrap?: boolean;
}

/**
 * Navigation handler component for lists
 */
export function NavigationHandler({
  items,
  selectedIndex,
  onSelectionChange,
  onSelect,
  disabled = false,
  wrap = true
}: NavigationHandlerProps): React.JSX.Element | null {
  const handleArrowUp = () => {
    if (selectedIndex > 0) {
      onSelectionChange(selectedIndex - 1);
    } else if (wrap && items.length > 0) {
      onSelectionChange(items.length - 1);
    }
  };

  const handleArrowDown = () => {
    if (selectedIndex < items.length - 1) {
      onSelectionChange(selectedIndex + 1);
    } else if (wrap && items.length > 0) {
      onSelectionChange(0);
    }
  };

  const handleEnter = () => {
    if (onSelect && selectedIndex >= 0 && selectedIndex < items.length) {
      const item = items[selectedIndex];
      if (item) {
        onSelect(selectedIndex, item);
      }
    }
  };

  return (
    <KeyboardHandler
      onArrowUp={handleArrowUp}
      onArrowDown={handleArrowDown}
      onEnter={handleEnter}
      disabled={disabled}
    />
  );
}

/**
 * Hotkey handler props
 */
export interface HotkeyHandlerProps {
  hotkeys: Record<string, () => void>;
  disabled?: boolean;
}

/**
 * Hotkey handler component
 */
export function HotkeyHandler({
  hotkeys,
  disabled = false
}: HotkeyHandlerProps): React.JSX.Element | null {
  const handleKeyPress = (input: string, key: any) => {
    // Handle function keys
    if (key.f1 && hotkeys.f1) hotkeys.f1();
    if (key.f2 && hotkeys.f2) hotkeys.f2();
    if (key.f3 && hotkeys.f3) hotkeys.f3();
    if (key.f4 && hotkeys.f4) hotkeys.f4();
    if (key.f5 && hotkeys.f5) hotkeys.f5();
    if (key.f6 && hotkeys.f6) hotkeys.f6();
    if (key.f7 && hotkeys.f7) hotkeys.f7();
    if (key.f8 && hotkeys.f8) hotkeys.f8();
    if (key.f9 && hotkeys.f9) hotkeys.f9();
    if (key.f10 && hotkeys.f10) hotkeys.f10();
    if (key.f11 && hotkeys.f11) hotkeys.f11();
    if (key.f12 && hotkeys.f12) hotkeys.f12();

    // Handle character keys
    if (input) {
      const handler = hotkeys[input.toLowerCase()];
      if (handler) {
        handler();
      }
    }

    // Handle special combinations
    if (key.ctrl) {
      const ctrlKey = `ctrl+${input}`;
      if (hotkeys[ctrlKey]) {
        hotkeys[ctrlKey]();
      }
    }
  };

  return (
    <KeyboardHandler
      onKeyPress={handleKeyPress}
      disabled={disabled}
    />
  );
}
