import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { useInput } from 'ink';
/**
 * Keyboard handler component
 */
export function KeyboardHandler({ onKeyPress, onArrowUp, onArrowDown, onArrowLeft, onArrowRight, onEnter, onEscape, onTab, onBackspace, onDelete, onHome, onEnd, onPageUp, onPageDown, disabled = false }) {
    useInput((input, key) => {
        if (disabled)
            return;
        // Call general key press handler
        if (onKeyPress) {
            onKeyPress(input, key);
        }
        // Handle specific keys
        if (key.upArrow && onArrowUp) {
            onArrowUp();
        }
        else if (key.downArrow && onArrowDown) {
            onArrowDown();
        }
        else if (key.leftArrow && onArrowLeft) {
            onArrowLeft();
        }
        else if (key.rightArrow && onArrowRight) {
            onArrowRight();
        }
        else if (key.return && onEnter) {
            onEnter();
        }
        else if (key.escape && onEscape) {
            onEscape();
        }
        else if (key.tab && onTab) {
            onTab();
        }
        else if (key.backspace && onBackspace) {
            onBackspace();
        }
        else if (key.delete && onDelete) {
            onDelete();
        }
        else if (key.home && onHome) {
            onHome();
        }
        else if (key.end && onEnd) {
            onEnd();
        }
        else if (key.pageUp && onPageUp) {
            onPageUp();
        }
        else if (key.pageDown && onPageDown) {
            onPageDown();
        }
    }, { isActive: !disabled });
    return null; // This component doesn't render anything
}
/**
 * Navigation handler component for lists
 */
export function NavigationHandler({ items, selectedIndex, onSelectionChange, onSelect, disabled = false, wrap = true }) {
    const handleArrowUp = () => {
        if (selectedIndex > 0) {
            onSelectionChange(selectedIndex - 1);
        }
        else if (wrap && items.length > 0) {
            onSelectionChange(items.length - 1);
        }
    };
    const handleArrowDown = () => {
        if (selectedIndex < items.length - 1) {
            onSelectionChange(selectedIndex + 1);
        }
        else if (wrap && items.length > 0) {
            onSelectionChange(0);
        }
    };
    const handleEnter = () => {
        if (onSelect && selectedIndex >= 0 && selectedIndex < items.length) {
            const item = items[selectedIndex];
            if (item) {
                onSelect(selectedIndex, item);
            }
        }
    };
    return (_jsx(KeyboardHandler, { onArrowUp: handleArrowUp, onArrowDown: handleArrowDown, onEnter: handleEnter, disabled: disabled }));
}
/**
 * Hotkey handler component
 */
export function HotkeyHandler({ hotkeys, disabled = false }) {
    const handleKeyPress = (input, key) => {
        // Handle function keys
        if (key.f1 && hotkeys.f1)
            hotkeys.f1();
        if (key.f2 && hotkeys.f2)
            hotkeys.f2();
        if (key.f3 && hotkeys.f3)
            hotkeys.f3();
        if (key.f4 && hotkeys.f4)
            hotkeys.f4();
        if (key.f5 && hotkeys.f5)
            hotkeys.f5();
        if (key.f6 && hotkeys.f6)
            hotkeys.f6();
        if (key.f7 && hotkeys.f7)
            hotkeys.f7();
        if (key.f8 && hotkeys.f8)
            hotkeys.f8();
        if (key.f9 && hotkeys.f9)
            hotkeys.f9();
        if (key.f10 && hotkeys.f10)
            hotkeys.f10();
        if (key.f11 && hotkeys.f11)
            hotkeys.f11();
        if (key.f12 && hotkeys.f12)
            hotkeys.f12();
        // Handle character keys
        if (input) {
            const handler = hotkeys[input.toLowerCase()];
            if (handler) {
                handler();
            }
        }
        // Handle special combinations
        if (key.ctrl) {
            const ctrlKey = `ctrl+${input}`;
            if (hotkeys[ctrlKey]) {
                hotkeys[ctrlKey]();
            }
        }
    };
    return (_jsx(KeyboardHandler, { onKeyPress: handleKeyPress, disabled: disabled }));
}
//# sourceMappingURL=KeyboardHandler.js.map