/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import type { AIManager } from '@inkbytefo/s647-core-refactored';
import type { Configuration, Logger } from '@inkbytefo/s647-shared';
import type { Screen } from '../App.js';
interface ChatScreenProps {
    aiManager: AIManager;
    config: Configuration;
    logger: Logger;
    onNavigate: (screen: Screen) => void;
}
export declare function ChatScreen({ aiManager, config, logger, onNavigate }: ChatScreenProps): React.JSX.Element;
export {};
//# sourceMappingURL=ChatScreen.d.ts.map