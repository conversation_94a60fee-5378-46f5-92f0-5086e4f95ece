import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 inkbytefo
 * SPDX-License-Identifier: Apache-2.0
 */
import React, { useState, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { HotkeyHandler } from '../components/KeyboardHandler.js';
export function ChatScreen({ aiManager, config, logger, onNavigate }) {
    const [state, setState] = useState({
        messages: [
            {
                id: '1',
                role: 'system',
                content: 'Welcome to S647 AI Chat! Select a provider and start chatting.',
                timestamp: new Date(),
            }
        ],
        currentProvider: config.defaultProvider,
        isTyping: false,
        inputBuffer: '',
        showProviderSelector: false,
    });
    const sendMessage = useCallback(async (content) => {
        if (!content.trim())
            return;
        const userMessage = {
            id: Date.now().toString(),
            role: 'user',
            content: content.trim(),
            timestamp: new Date(),
        };
        setState(prev => ({
            ...prev,
            messages: [...prev.messages, userMessage],
            isTyping: true,
            inputBuffer: '',
        }));
        try {
            logger.info(`Sending message to ${state.currentProvider}...`);
            // TODO: Implement actual AI provider communication
            // Simulate AI response
            await new Promise(resolve => setTimeout(resolve, 1000));
            const assistantMessage = {
                id: (Date.now() + 1).toString(),
                role: 'assistant',
                content: `This is a simulated response from ${state.currentProvider}. The actual AI integration will be implemented in future versions.`,
                timestamp: new Date(),
                provider: state.currentProvider,
            };
            setState(prev => ({
                ...prev,
                messages: [...prev.messages, assistantMessage],
                isTyping: false,
            }));
            logger.info('Message sent successfully');
        }
        catch (error) {
            logger.error(`Failed to send message: ${error}`);
            const errorMessage = {
                id: (Date.now() + 2).toString(),
                role: 'system',
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                timestamp: new Date(),
            };
            setState(prev => ({
                ...prev,
                messages: [...prev.messages, errorMessage],
                isTyping: false,
            }));
        }
    }, [aiManager, logger, state.currentProvider]);
    const handleInput = useCallback((input, key) => {
        if (key.return) {
            if (state.inputBuffer.trim()) {
                sendMessage(state.inputBuffer);
            }
        }
        else if (key.backspace) {
            setState(prev => ({
                ...prev,
                inputBuffer: prev.inputBuffer.slice(0, -1),
            }));
        }
        else if (input && !key.ctrl && !key.meta) {
            setState(prev => ({
                ...prev,
                inputBuffer: prev.inputBuffer + input,
            }));
        }
    }, [state.inputBuffer, sendMessage]);
    const handleHotkeys = {
        'p': () => {
            setState(prev => ({ ...prev, showProviderSelector: !prev.showProviderSelector }));
        },
        'c': () => {
            setState(prev => ({ ...prev, messages: [], inputBuffer: '' }));
            logger.info('Chat cleared');
        },
        'f5': () => onNavigate('chat'),
        'f6': () => onNavigate('config'),
        'f7': () => onNavigate('providers'),
        'f8': () => onNavigate('tools'),
    };
    // Use input hook for chat input
    useInput(handleInput, { isActive: !state.showProviderSelector });
    const availableProviders = Object.keys(config.providers).filter(name => config.providers[name]?.enabled);
    const getMessageColor = (role) => {
        switch (role) {
            case 'user': return 'blue';
            case 'assistant': return 'green';
            case 'system': return 'yellow';
            default: return 'white';
        }
    };
    const getMessagePrefix = (role) => {
        switch (role) {
            case 'user': return '👤 You';
            case 'assistant': return '🤖 AI';
            case 'system': return '⚙️ System';
            default: return '❓';
        }
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, height: "100%", children: [_jsxs(Box, { marginBottom: 1, justifyContent: "space-between", children: [_jsx(Text, { color: "cyan", bold: true, children: "\uD83D\uDCAC AI Chat Interface" }), _jsxs(Text, { color: "cyan", children: ["Provider: ", state.currentProvider] })] }), _jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "gray", children: "Type your message and press Enter | Clear: C | Switch Provider: P | Screens: F5-F8" }) }), state.showProviderSelector && (_jsx(Box, { marginBottom: 1, borderStyle: "single", padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "cyan", bold: true, children: "Select Provider:" }), availableProviders.map((provider, index) => (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { color: provider === state.currentProvider ? 'green' : 'white', children: [index + 1, ". ", provider, " ", provider === state.currentProvider ? '(current)' : ''] }) }, provider))), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: "Press P to close" }) })] }) })), _jsxs(Box, { flexDirection: "column", flexGrow: 1, marginBottom: 1, children: [state.messages.map((message) => (_jsx(Box, { marginBottom: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsxs(Box, { children: [_jsx(Text, { color: getMessageColor(message.role), bold: true, children: getMessagePrefix(message.role) }), _jsx(Box, { marginLeft: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: message.timestamp.toLocaleTimeString() }) }), message.provider && (_jsx(Box, { marginLeft: 1, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["via ", message.provider] }) }))] }), _jsx(Box, { marginLeft: 2, marginTop: 1, children: _jsx(Text, { color: "white", children: message.content }) })] }) }, message.id))), state.isTyping && (_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "green", bold: true, children: "\uD83E\uDD16 AI is typing..." }) }))] }), _jsx(Box, { borderStyle: "single", borderTop: true, padding: 1, children: _jsxs(Box, { children: [_jsx(Text, { color: "blue", children: "\uD83D\uDCAC Message:" }), _jsx(Box, { marginLeft: 1, children: _jsxs(Text, { color: "white", children: [state.inputBuffer, _jsx(Text, { color: "gray", children: "\u2588" })] }) })] }) }), _jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "cyan", children: ["Messages: ", state.messages.length, " | Provider: ", state.currentProvider, " | Status: ", state.isTyping ? 'Processing...' : 'Ready'] }) }), _jsx(HotkeyHandler, { hotkeys: handleHotkeys, disabled: state.isTyping })] }));
}
//# sourceMappingURL=ChatScreen.js.map